"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/hooks/use-toast"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Plus, Edit, Trash2, Save, X, Upload, FileText, Image, Video, Settings, Search, Filter, ChevronDown, MoreHorizontal, Calendar, AlertCircle, Package, Tag, Folder } from "lucide-react"
import { AuthGuard } from "@/components/auth-guard"

interface Category {
  id: number
  name: string
  nameAr?: string
  slug: string
  description?: string
  descriptionAr?: string
  image?: string
  icon?: string
  sortOrder: number
  isActive: boolean
  productCount: number
  createdAt: string
  updatedAt: string
}

export default function AdminCategoriesPage() {
  const [categories, setCategories] = useState<Category[]>([])
  const [filteredCategories, setFilteredCategories] = useState<Category[]>([])
  const [editingCategory, setEditingCategory] = useState<Category | null>(null)
  const [isCreating, setIsCreating] = useState(false)
  const { toast } = useToast()

  // Filter states
  const [filters, setFilters] = useState({
    categoryId: "",
    categoryName: "",
    status: "",
    fromDate: "",
    toDate: "",
  })
  
  // UI states
  const [showFilters, setShowFilters] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(10)
  const [sortField, setSortField] = useState<string>("sortOrder")
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc")
  const [uploadingImage, setUploadingImage] = useState(false)

  const [formData, setFormData] = useState({
    name: "",
    nameAr: "",
    description: "",
    descriptionAr: "",
    image: "",
    icon: "",
    sortOrder: 0,
    isActive: true,
  })

  useEffect(() => {
    loadCategories()
  }, [])

  useEffect(() => {
    applyFilters()
  }, [categories, filters, sortField, sortOrder])

  const loadCategories = async () => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'https://localhost:56266/api'}/category/admin`, {
        headers: {
          'Authorization': `Bearer ${JSON.parse(sessionStorage.getItem('adminSession') || '{}').token}`
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        setCategories(data)
      } else {
        // Fallback to empty array if API fails
        setCategories([])
        toast({
          title: "API connection failed",
          description: "Using local data",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('Failed to load categories:', error)
      setCategories([])
      toast({
        title: "Error",
        description: "Failed to load categories",
        variant: "destructive",
      })
    }
  }

  const applyFilters = () => {
    let filtered = [...categories]

    // Apply filters
    if (filters.categoryId) {
      filtered = filtered.filter(c => c.id.toString().includes(filters.categoryId))
    }
    if (filters.categoryName) {
      filtered = filtered.filter(c => 
        c.name.toLowerCase().includes(filters.categoryName.toLowerCase()) ||
        (c.nameAr && c.nameAr.toLowerCase().includes(filters.categoryName.toLowerCase()))
      )
    }
    if (filters.status) {
      filtered = filtered.filter(c => 
        filters.status === "active" ? c.isActive : !c.isActive
      )
    }
    if (filters.fromDate) {
      const fromDate = new Date(filters.fromDate)
      filtered = filtered.filter(c => new Date(c.updatedAt) >= fromDate)
    }
    if (filters.toDate) {
      const toDate = new Date(filters.toDate)
      toDate.setHours(23, 59, 59, 999)
      filtered = filtered.filter(c => new Date(c.updatedAt) <= toDate)
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any = a[sortField as keyof Category]
      let bValue: any = b[sortField as keyof Category]

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase()
        bValue = bValue.toLowerCase()
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : aValue < bValue ? -1 : 0
      } else {
        return aValue < bValue ? 1 : aValue > bValue ? -1 : 0
      }
    })

    setFilteredCategories(filtered)
  }

  const resetFilters = () => {
    setFilters({
      categoryId: "",
      categoryName: "",
      status: "",
      fromDate: "",
      toDate: "",
    })
  }

  const handleCreate = () => {
    setIsCreating(true)
    setFormData({
      name: "",
      nameAr: "",
      description: "",
      descriptionAr: "",
      image: "",
      icon: "",
      sortOrder: categories.length,
      isActive: true,
    })
  }

  const handleEdit = (category: Category) => {
    setEditingCategory(category)
    setFormData({
      name: category.name,
      nameAr: category.nameAr || "",
      description: category.description || "",
      descriptionAr: category.descriptionAr || "",
      image: category.image || "",
      icon: category.icon || "",
      sortOrder: category.sortOrder,
      isActive: category.isActive,
    })
  }

  const handleSave = async () => {
    try {
      const sessionRaw = sessionStorage.getItem('adminSession')
      const session = sessionRaw ? JSON.parse(sessionRaw) : null
      const token = session?.token

      if (!token) {
        toast({
          title: "Authentication required",
          description: "Please log in to manage categories",
          variant: "destructive",
        })
        return
      }

      const method = editingCategory ? 'PUT' : 'POST'
      const url = editingCategory 
        ? `${process.env.NEXT_PUBLIC_API_URL || 'https://localhost:56266/api'}/category/${editingCategory.id}`
        : `${process.env.NEXT_PUBLIC_API_URL || 'https://localhost:56266/api'}/category`

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(formData)
      })

      if (!response.ok) {
        throw new Error('Failed to save category')
      }

      toast({
        title: editingCategory ? "Category updated successfully!" : "Category created successfully!",
      })

      loadCategories()
      handleCancel()
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save category",
        variant: "destructive",
      })
    }
  }

  const handleDelete = async (id: number) => {
    if (!confirm("Are you sure you want to delete this category?")) {
      return
    }

    try {
      const sessionRaw = sessionStorage.getItem('adminSession')
      const session = sessionRaw ? JSON.parse(sessionRaw) : null
      const token = session?.token

      if (!token) {
        toast({
          title: "Authentication required",
          description: "Please log in to manage categories",
          variant: "destructive",
        })
        return
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'https://localhost:56266/api'}/category/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to delete category')
      }

      toast({ title: "Category deleted successfully!" })
      loadCategories()
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to delete category",
        variant: "destructive",
      })
    }
  }

  const handleCancel = () => {
    setEditingCategory(null)
    setIsCreating(false)
    setFormData({
      name: "",
      nameAr: "",
      description: "",
      descriptionAr: "",
      image: "",
      icon: "",
      sortOrder: 0,
      isActive: true,
    })
  }

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setUploadingImage(true)
    try {
      const formDataUpload = new FormData()
      formDataUpload.append('file', file)
      const sessionRaw = sessionStorage.getItem('adminSession')
      const session = sessionRaw ? JSON.parse(sessionRaw) : null
      const token = session?.token

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'https://localhost:56266/api'}/fileupload/image`, {
        method: 'POST',
        headers: token ? { 'Authorization': `Bearer ${token}` } : undefined,
        body: formDataUpload
      })

      if (!response.ok) {
        throw new Error('Upload failed')
      }

      const result = await response.json()
      setFormData(prev => ({ ...prev, image: result.url }))
      toast({ title: "Image uploaded successfully!" })
    } catch (error) {
      toast({
        title: "Upload failed",
        description: "Failed to upload image",
        variant: "destructive",
      })
    } finally {
      setUploadingImage(false)
    }
  }

  // Pagination calculations
  const totalPages = Math.ceil(filteredCategories.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentCategories = filteredCategories.slice(startIndex, endIndex)

  return (
    <AuthGuard>
      <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
                <Tag className="h-8 w-8" />
                Categories Management
              </h1>
              <p className="text-gray-600">Manage product categories and organization</p>
            </div>
            <div className="flex gap-3">
              <Button onClick={handleCreate} className="bg-blue-600 hover:bg-blue-700 text-white">
                <Plus className="h-4 w-4 mr-2" />
                Add New Category
              </Button>
            </div>
          </div>

          {/* Search Filters Card */}
          <Card className="mb-6">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Search className="h-5 w-5" />
                  Search Categories
                </CardTitle>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => setShowFilters(!showFilters)}
                >
                  <Filter className="h-4 w-4 mr-2" />
                  {showFilters ? 'Hide Filters' : 'Show Filters'}
                </Button>
              </div>
            </CardHeader>
            
            {showFilters && (
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                  {/* Category ID */}
                  <div className="space-y-2">
                    <Label htmlFor="categoryId">Category ID:</Label>
                    <Input
                      id="categoryId"
                      placeholder="Category ID"
                      value={filters.categoryId}
                      onChange={(e) => setFilters({...filters, categoryId: e.target.value})}
                    />
                  </div>

                  {/* Category Name */}
                  <div className="space-y-2">
                    <Label htmlFor="categoryName">Category Name:</Label>
                    <Input
                      id="categoryName"
                      placeholder="Category Name"
                      value={filters.categoryName}
                      onChange={(e) => setFilters({...filters, categoryName: e.target.value})}
                    />
                  </div>

                  {/* Status */}
                  <div className="space-y-2">
                    <Label htmlFor="status">Status:</Label>
                    <Select
                      value={filters.status}
                      onValueChange={(value) => setFilters({...filters, status: value})}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select Status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">All Status</SelectItem>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="inactive">Inactive</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Date Filters */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  <div className="space-y-2">
                    <Label htmlFor="fromDate">From Date:</Label>
                    <Input
                      id="fromDate"
                      type="date"
                      value={filters.fromDate}
                      onChange={(e) => setFilters({...filters, fromDate: e.target.value})}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="toDate">To Date:</Label>
                    <Input
                      id="toDate"
                      type="date"
                      value={filters.toDate}
                      onChange={(e) => setFilters({...filters, toDate: e.target.value})}
                    />
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-4">
                  <Button onClick={resetFilters} variant="outline">
                    Reset
                  </Button>
                  <Button onClick={applyFilters} className="bg-blue-600 hover:bg-blue-700">
                    <Search className="h-4 w-4 mr-2" />
                    Search
                  </Button>
                </div>
              </CardContent>
            )}
          </Card>

          {/* Categories List Table */}
          <Card>
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2">
                <Folder className="h-5 w-5" />
                Categories List
              </CardTitle>
            </CardHeader>
            
            <CardContent>
              {/* Table Controls */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  <Label htmlFor="itemsPerPage">Show:</Label>
                  <Select
                    value={itemsPerPage.toString()}
                    onValueChange={(value) => {
                      setItemsPerPage(Number(value))
                      setCurrentPage(1)
                    }}
                  >
                    <SelectTrigger className="w-20">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="10">10</SelectItem>
                      <SelectItem value="25">25</SelectItem>
                      <SelectItem value="50">50</SelectItem>
                      <SelectItem value="100">100</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="text-sm text-gray-600">
                  Showing {startIndex + 1} to {Math.min(endIndex, filteredCategories.length)} of {filteredCategories.length} entries
                </div>
              </div>

              {/* Data Table */}
              <div className="border rounded-lg overflow-hidden">
                <Table>
                  <TableHeader className="bg-teal-600">
                    <TableRow>
                      <TableHead className="text-white font-semibold">ID</TableHead>
                      <TableHead className="text-white font-semibold">Image</TableHead>
                      <TableHead className="text-white font-semibold">Name</TableHead>
                      <TableHead className="text-white font-semibold">Description</TableHead>
                      <TableHead className="text-white font-semibold">Products</TableHead>
                      <TableHead className="text-white font-semibold">Status</TableHead>
                      <TableHead className="text-white font-semibold">Sort Order</TableHead>
                      <TableHead className="text-white font-semibold">Updated</TableHead>
                      <TableHead className="text-white font-semibold text-center">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {currentCategories.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={9} className="text-center py-8 text-gray-500">
                          No categories found
                        </TableCell>
                      </TableRow>
                    ) : (
                      currentCategories.map((category, index) => (
                        <TableRow key={category.id} className={index % 2 === 0 ? "bg-gray-50" : "bg-white"}>
                          <TableCell className="font-medium">{category.id}</TableCell>
                          <TableCell>
                            {category.image ? (
                              <img 
                                src={category.image} 
                                alt={category.name}
                                className="w-12 h-12 object-cover rounded border"
                              />
                            ) : (
                              <div className="w-12 h-12 bg-gray-200 rounded border flex items-center justify-center">
                                <Folder className="h-5 w-5 text-gray-400" />
                              </div>
                            )}
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium">{category.name}</div>
                              {category.nameAr && (
                                <div className="text-sm text-gray-500">{category.nameAr}</div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="max-w-xs truncate">
                              {category.description || <span className="text-gray-400">No description</span>}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline" className="bg-blue-50 text-blue-700">
                              {category.productCount} products
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge 
                              variant={category.isActive ? "default" : "secondary"}
                              className={category.isActive ? "bg-green-100 text-green-800" : ""}
                            >
                              {category.isActive ? "Active" : "Inactive"}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <span className="font-medium">{category.sortOrder}</span>
                          </TableCell>
                          <TableCell>
                            {new Date(category.updatedAt).toLocaleDateString('en-US', {
                              day: '2-digit',
                              month: 'short',
                              year: 'numeric'
                            })}
                          </TableCell>
                          <TableCell>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => handleEdit(category)}>
                                  <Edit className="h-4 w-4 mr-2" />
                                  Edit
                                </DropdownMenuItem>
                                <DropdownMenuItem 
                                  onClick={() => handleDelete(category.id)}
                                  className="text-red-600"
                                  disabled={category.productCount > 0}
                                >
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Delete
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between mt-4">
                  <div className="text-sm text-gray-600">
                    Page {currentPage} of {totalPages}
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                    >
                      Previous
                    </Button>
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      const pageNumber = i + 1
                      return (
                        <Button
                          key={pageNumber}
                          variant={currentPage === pageNumber ? "default" : "outline"}
                          size="sm"
                          onClick={() => setCurrentPage(pageNumber)}
                        >
                          {pageNumber}
                        </Button>
                      )
                    })}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Create/Edit Category Dialog */}
          {(isCreating || editingCategory) && (
            <Card className="mt-8">
              <CardHeader>
                <CardTitle>{editingCategory ? "Edit Category" : "Create New Category"}</CardTitle>
                <CardDescription>
                  {editingCategory ? "Update category information" : "Add a new category to organize products"}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Category Name (English)</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      placeholder="Enter category name"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="nameAr">Category Name (Arabic)</Label>
                    <Input
                      id="nameAr"
                      value={formData.nameAr}
                      onChange={(e) => setFormData({ ...formData, nameAr: e.target.value })}
                      placeholder="Enter Arabic name"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="description">Description (English)</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      placeholder="Enter category description"
                      rows={3}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="descriptionAr">Description (Arabic)</Label>
                    <Textarea
                      id="descriptionAr"
                      value={formData.descriptionAr}
                      onChange={(e) => setFormData({ ...formData, descriptionAr: e.target.value })}
                      placeholder="Enter Arabic description"
                      rows={3}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="image">Category Image</Label>
                  <div className="flex gap-4">
                    <div className="flex-1">
                      <Input
                        id="image"
                        value={formData.image}
                        onChange={(e) => setFormData({ ...formData, image: e.target.value })}
                        placeholder="Enter image URL or upload file"
                      />
                    </div>
                    <div className="relative">
                      <input
                        type="file"
                        id="imageUpload"
                        accept="image/*"
                        onChange={handleImageUpload}
                        className="absolute inset-0 opacity-0 cursor-pointer"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        disabled={uploadingImage}
                        className="w-full"
                      >
                        {uploadingImage ? (
                          <>Uploading...</>
                        ) : (
                          <>
                            <Image className="h-4 w-4 mr-2" />
                            Upload
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                  {formData.image && (
                    <div className="mt-2">
                      <img 
                        src={formData.image} 
                        alt="Preview" 
                        className="w-32 h-32 object-cover rounded border"
                      />
                    </div>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="icon">Icon (CSS class or emoji)</Label>
                    <Input
                      id="icon"
                      value={formData.icon}
                      onChange={(e) => setFormData({ ...formData, icon: e.target.value })}
                      placeholder="🏗️ or icon-name"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="sortOrder">Sort Order</Label>
                    <Input
                      id="sortOrder"
                      type="number"
                      value={formData.sortOrder}
                      onChange={(e) => setFormData({ ...formData, sortOrder: Number(e.target.value) })}
                      placeholder="0"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="isActive">Status</Label>
                    <Select
                      value={formData.isActive.toString()}
                      onValueChange={(value) => setFormData({ ...formData, isActive: value === "true" })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="true">Active</SelectItem>
                        <SelectItem value="false">Inactive</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="flex gap-4">
                  <Button onClick={handleSave} className="bg-green-600 hover:bg-green-700">
                    <Save className="h-4 w-4 mr-2" />
                    Save Category
                  </Button>
                  <Button onClick={handleCancel} variant="outline">
                    <X className="h-4 w-4 mr-2" />
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
      </div>
    </AuthGuard>
  )
}
