"use client"

import type React from "react"
import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  Home, 
  Package, 
  FolderOpen, 
  MessageSquare, 
  Settings, 
  LogOut, 
  Tag, 
  Menu, 
  X,
  Bell,
  Search,
  ChevronDown,
  User,
  BarChart3,
  Globe,
  Shield
} from "lucide-react"
import { cn } from "@/lib/utils"

export default function AdminDashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [userInfo, setUserInfo] = useState<any>(null)
  const pathname = usePathname()

  useEffect(() => {
    // Get user info from session
    if (typeof window !== "undefined") {
      const sessionRaw = sessionStorage.getItem("adminSession")
      if (sessionRaw) {
        try {
          const session = JSON.parse(sessionRaw)
          setUserInfo(session)
        } catch (error) {
          console.error("Failed to parse session:", error)
        }
      }
    }
  }, [])

  const handleLogout = () => {
    if (typeof window !== "undefined") {
      sessionStorage.removeItem("adminSession")
      window.location.href = "/admin"
    }
  }

  const navigation = [
    { 
      name: "Dashboard", 
      href: "/admin/dashboard", 
      icon: Home,
      description: "Overview and analytics",
      count: null
    },
    { 
      name: "Products", 
      href: "/admin/dashboard/products", 
      icon: Package,
      description: "Manage product catalog",
      count: null
    },
    { 
      name: "Categories", 
      href: "/admin/dashboard/categories", 
      icon: Tag,
      description: "Product categories",
      count: null
    },
    { 
      name: "Projects", 
      href: "/admin/dashboard/projects", 
      icon: FolderOpen,
      description: "Portfolio management",
      count: null
    },
    { 
      name: "Messages", 
      href: "/admin/dashboard/messages", 
      icon: MessageSquare,
      description: "Contact inquiries",
      count: 3
    },
    { 
      name: "Settings", 
      href: "/admin/dashboard/settings", 
      icon: Settings,
      description: "System configuration",
      count: null
    },
  ]

  const quickActions = [
    { name: "Add Product", href: "/admin/dashboard/products", icon: Package },
    { name: "View Messages", href: "/admin/dashboard/messages", icon: MessageSquare },
    { name: "Analytics", href: "/admin/dashboard", icon: BarChart3 },
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Sidebar */}
      <div className={cn(
        "fixed inset-y-0 left-0 z-50 w-72 transform bg-white shadow-xl transition-transform duration-300 ease-in-out border-r border-gray-200",
        sidebarOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0"
      )}>
        {/* Sidebar Header */}
        <div className="flex h-16 items-center justify-between px-6 border-b border-gray-200">
          <Link href="/admin/dashboard" className="flex items-center space-x-3">
            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-600">
              <span className="text-white font-bold text-sm">D</span>
            </div>
            <div>
              <h1 className="text-lg font-semibold text-gray-900">DRYLEX</h1>
              <p className="text-xs text-gray-500">Admin Dashboard</p>
            </div>
          </Link>
          <Button
            variant="ghost"
            size="sm"
            className="lg:hidden"
            onClick={() => setSidebarOpen(false)}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Navigation */}
        <div className="flex-1 overflow-y-auto py-6">
          <nav className="space-y-1 px-4">
            {navigation.map((item) => {
              const isActive = pathname === item.href
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={cn(
                    "group flex items-center justify-between rounded-lg px-3 py-3 text-sm font-medium transition-colors hover:bg-gray-50",
                    isActive 
                      ? "bg-blue-50 text-blue-700 border-r-2 border-blue-600" 
                      : "text-gray-700 hover:text-gray-900"
                  )}
                  onClick={() => setSidebarOpen(false)}
                >
                  <div className="flex items-center space-x-3">
                    <item.icon className={cn(
                      "h-5 w-5 flex-shrink-0",
                      isActive ? "text-blue-600" : "text-gray-400 group-hover:text-gray-500"
                    )} />
                    <div>
                      <div className="font-medium">{item.name}</div>
                      <div className="text-xs text-gray-500">{item.description}</div>
                    </div>
                  </div>
                  {item.count && (
                    <Badge variant="secondary" className="bg-red-100 text-red-800 text-xs">
                      {item.count}
                    </Badge>
                  )}
                </Link>
              )
            })}
          </nav>

          <Separator className="my-6 mx-4" />

          {/* Quick Actions */}
          <div className="px-4">
            <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">
              Quick Actions
            </h3>
            <div className="space-y-2">
              {quickActions.map((action) => (
                <Link
                  key={action.name}
                  href={action.href}
                  className="flex items-center space-x-2 rounded-md px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 hover:text-gray-900 transition-colors"
                  onClick={() => setSidebarOpen(false)}
                >
                  <action.icon className="h-4 w-4" />
                  <span>{action.name}</span>
                </Link>
              ))}
            </div>
          </div>
        </div>

        {/* User Profile */}
        <div className="border-t border-gray-200 p-4">
          <div className="flex items-center space-x-3">
            <Avatar className="h-10 w-10">
              <AvatarImage src="/placeholder-user.jpg" alt="Admin User" />
              <AvatarFallback className="bg-blue-600 text-white">
                {userInfo?.firstName?.[0] || 'A'}{userInfo?.lastName?.[0] || 'U'}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">
                {userInfo?.firstName || 'Admin'} {userInfo?.lastName || 'User'}
              </p>
              <p className="text-xs text-gray-500 truncate">
                {userInfo?.email || '<EMAIL>'}
              </p>
            </div>
            <Button variant="ghost" size="sm" onClick={handleLogout} title="Logout">
              <LogOut className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="lg:ml-72">
        {/* Top Header */}
        <header className="sticky top-0 z-40 bg-white border-b border-gray-200 shadow-sm">
          <div className="flex h-16 items-center justify-between px-6">
            {/* Mobile menu button */}
            <Button
              variant="ghost"
              size="sm"
              className="lg:hidden"
              onClick={() => setSidebarOpen(true)}
            >
              <Menu className="h-5 w-5" />
            </Button>

            {/* Header Title */}
            <div className="flex-1 lg:flex-none">
              <h1 className="text-lg font-semibold text-gray-900 capitalize">
                {pathname.split('/').pop()?.replace('-', ' ') || 'Dashboard'}
              </h1>
            </div>

            {/* Header Actions */}
            <div className="flex items-center space-x-4">
              {/* Quick Search */}
              <div className="hidden md:flex items-center space-x-2">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Quick search..."
                    className="pl-10 pr-4 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Notifications */}
              <Button variant="ghost" size="sm" className="relative">
                <Bell className="h-5 w-5" />
                <Badge className="absolute -top-1 -right-1 h-5 w-5 rounded-full bg-red-500 text-white text-xs flex items-center justify-center">
                  3
                </Badge>
              </Button>

              {/* View Website */}
              <Button variant="outline" size="sm" asChild>
                <Link href="/" target="_blank">
                  <Globe className="h-4 w-4 mr-2" />
                  View Site
                </Link>
              </Button>

              {/* User Menu */}
              <div className="flex items-center space-x-2">
                <Avatar className="h-8 w-8">
                  <AvatarImage src="/placeholder-user.jpg" alt="Admin User" />
                  <AvatarFallback className="bg-blue-600 text-white text-xs">
                    {userInfo?.firstName?.[0] || 'A'}{userInfo?.lastName?.[0] || 'U'}
                  </AvatarFallback>
                </Avatar>
                <ChevronDown className="h-4 w-4 text-gray-400" />
              </div>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="flex-1">
          {children}
        </main>
      </div>

      {/* Mobile Sidebar Overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  )
}
