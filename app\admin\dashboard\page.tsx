"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { BarChart3, Users, Package, MessageSquare, Settings, Plus, Eye, Download, Reply, Briefcase } from "lucide-react"
import Link from "next/link"
import { dashboardApi, ProductDto, ProjectDto, ContactDto } from "@/lib/api"
import { db } from "@/lib/database"
import type { Product, Project, ContactMessage } from "@/lib/database"
import { AuthGuard } from "@/components/auth-guard"


// JSON-LD for Admin Dashboard Page
const adminDashboardJsonLd = {
  "@context": "https://schema.org",
  "@type": "WebPage",
  name: "Admin Dashboard - DRYLEX Iraq",
  description: "Admin dashboard for managing DRYLEX Iraq website content. Manage products, projects, messages, and website settings from this central hub.",
  url: "https://drylexiraq.com/admin/dashboard",
  mainContentOfPage: {
    "@type": "WebApplication",
    name: "DRYLEX Iraq Admin Dashboard",
    description: "Web application for managing DRYLEX Iraq website content and settings",
    operatingSystem: "Web",
    applicationCategory: "BusinessApplication",
    offers: {
      "@type": "Offer",
      price: "0",
      priceCurrency: "USD",
      eligibleRegion: "IQ",
    },
  },
}

export default function DashboardPage() {
  // JSON-LD for WebPage
  const webPageJsonLd = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "Admin Dashboard - DRYLEX Iraq",
    "description": "Admin dashboard for managing DRYLEX Iraq construction materials website",
    "url": "https://drylexiraq.com/admin/dashboard",
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "Home",
          "item": "https://drylexiraq.com/"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "Admin",
          "item": "https://drylexiraq.com/admin"
        },
        {
          "@type": "ListItem",
          "position": 3,
          "name": "Dashboard",
          "item": "https://drylexiraq.com/admin/dashboard"
        }
      ]
    }
  }

  // JSON-LD for Organization
  const organizationJsonLd = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "DRYLEX IRAQ",
    "url": "https://drylexiraq.com",
    "logo": "https://drylexiraq.com/logo.png",
    "description": "Authorized distributor of Drylex products in Iraq providing high-performance construction materials and solutions",
    "foundingDate": "2015",
    "founders": [
      {
        "@type": "Person",
        "name": "Ali Al-Maliki"
      }
    ],
    "numberOfEmployees": "50",
    "industry": "Construction Materials",
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "Al-Muhandisin, Nasiriyah",
      "addressLocality": "Nasiriyah",
      "addressRegion": "Thi Qar Governorate",
      "postalCode": "64001",
      "addressCountry": "Iraq"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "+964 7812345678",
      "contactType": "customer service",
      "areaServed": "IQ",
      "availableLanguage": "en"
    },
    "sameAs": [
      "https://www.facebook.com/drylexiraq",
      "https://www.instagram.com/drylexiraq",
      "https://www.linkedin.com/company/drylex-iraq"
    ]
  }

  const [stats, setStats] = useState({
    totalProducts: 0,
    totalProjects: 0,
    totalServices: 0,
    pendingMessages: 0,
  })
  const [recentMessages, setRecentMessages] = useState<ContactDto[]>([])
  const [recentProducts, setRecentProducts] = useState<ProductDto[]>([])
  const [recentProjects, setRecentProjects] = useState<ProjectDto[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [useApi, setUseApi] = useState(true)

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      if (useApi) {
        // Try to load from API first
        const [statsData, recentProductsData, recentProjectsData, recentMessagesData] = await Promise.all([
          dashboardApi.getDashboardStats(),
          dashboardApi.getRecentProducts(5),
          dashboardApi.getRecentProjects(5),
          dashboardApi.getRecentMessages(5)
        ])
        
        setStats(statsData)
        setRecentProducts(recentProductsData)
        setRecentProjects(recentProjectsData)
        setRecentMessages(recentMessagesData)
      } else {
        // Fallback to local database
        loadFromLocalDatabase()
      }
    } catch (error) {
      console.error('Failed to load from API, falling back to local database:', error)
      setError('API connection failed, using local data')
      setUseApi(false)
      loadFromLocalDatabase()
    } finally {
      setIsLoading(false)
    }
  }

  const loadFromLocalDatabase = () => {
    try {
      // Load stats
      const products = db.getAllProducts()
      const projects = db.getProjects()
      const services = db.getAllServices()
      const messages = db.getMessages()

      setStats({
        totalProducts: products.length,
        totalProjects: projects.length,
        totalServices: services.length,
        pendingMessages: messages.filter((m) => m.status === "pending").length,
      })

      // Convert local data to API format
      setRecentMessages(messages.slice(0, 5).map(msg => ({
        ...msg,
        isRead: msg.status !== 'pending',
        isReplied: msg.status === 'replied',
        reply: undefined,
        repliedAt: undefined,
        repliedBy: undefined,
        inquiryType: undefined,
        preferredContactMethod: undefined,
        preferredContactTime: undefined
      })))
      setRecentProducts(products.slice(0, 5).map(product => ({
        ...product,
        slug: product.name.toLowerCase().replace(/\s+/g, '-'),
        shortDescription: product.description.substring(0, 100),
        imageUrl: product.image,
        category: product.category,
        isFeatured: product.featured,
        isActive: true,
        viewCount: 0,
        updatedAt: product.createdAt
      })))
      setRecentProjects(projects.slice(0, 5).map(project => ({
        id: project.id,
        title: project.name,
        slug: project.name.toLowerCase().replace(/\s+/g, '-'),
        description: project.description,
        shortDescription: project.description?.substring(0, 100) || '',
        imageUrl: project.afterImage || project.beforeImage || undefined,
        category: project.type,
        status: project.status,
        isFeatured: false,
        viewCount: 0,
        completedAt: project.date,
        createdAt: project.createdAt,
        updatedAt: project.updatedAt
      })))
    } catch (error) {
      console.error('Error loading from local database:', error)
      // Set default stats and empty arrays if local database fails
      setStats({
        totalProducts: 0,
        totalProjects: 0,
        totalServices: 0,
        pendingMessages: 0,
      })
      setRecentMessages([])
      setRecentProducts([])
      setRecentProjects([])
    }
  }

  const handleMarkAsRead = async (messageId: number) => {
    try {
      if (useApi) {
        // Try API first
        await dashboardApi.contactApi.markAsRead(messageId)
      } else {
        // Fallback to local database
        db.updateMessage(messageId, { status: "read" })
      }
      loadDashboardData()
    } catch (error) {
      console.error('Failed to mark message as read:', error)
      // Fallback to local database
      db.updateMessage(messageId, { status: "read" })
      loadDashboardData()
    }
  }

  const handleReply = (message: ContactDto) => {
    const subject = `Re: ${message.subject}`
    const body = `Dear ${message.name},\n\nThank you for contacting DRYLEX IRAQ Materials & Services.\n\n---\nOriginal Message:\n${message.message}`
    const mailtoUrl = `mailto:${message.email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`
    window.open(mailtoUrl)

    // Mark as replied (try API first, fallback to local)
    if (useApi) {
      // This would need a reply endpoint in the API
      console.log('Reply functionality would need API endpoint')
    } else {
      db.updateMessage(message.id, { status: "replied" })
    }
    loadDashboardData()
  }

  const handleLogout = () => {
    sessionStorage.removeItem("adminSession")
    window.location.href = "/admin"
  }

  return (
    <>
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(adminDashboardJsonLd) }} />
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(webPageJsonLd) }} />
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationJsonLd) }} />
      <AuthGuard>
        <div className="p-6">
            {/* Error Banner */}
            {error && (
              <div className="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-yellow-800 text-sm font-medium">{error}</p>
                    <p className="text-yellow-700 text-xs mt-1">
                      The dashboard is working with local data. Check if the backend API is running on https://localhost:56266
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => {
                        setUseApi(true)
                        setError(null)
                        loadDashboardData()
                      }}
                    >
                      Retry API
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => {
                        window.open('/test-api', '_blank')
                      }}
                    >
                      Test API
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Products</CardTitle>
                  <Package className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {isLoading ? '...' : stats.totalProducts}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {useApi ? 'From API' : 'From local data'}
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Projects</CardTitle>
                  <BarChart3 className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {isLoading ? '...' : stats.totalProjects}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {useApi ? 'From API' : 'From local data'}
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Services</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {isLoading ? '...' : stats.totalServices}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {useApi ? 'From API' : 'From local data'}
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Pending Messages</CardTitle>
                  <MessageSquare className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {isLoading ? '...' : stats.pendingMessages}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {useApi ? 'From API' : 'From local data'}
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Main Content */}
            <Tabs defaultValue="overview" className="space-y-6">
              <TabsList>
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="products">Products</TabsTrigger>
                <TabsTrigger value="projects">Projects</TabsTrigger>
                <TabsTrigger value="messages">Messages</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Recent Messages */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Recent Messages</CardTitle>
                      <CardDescription>Latest contact form submissions</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {isLoading ? (
                          <div className="text-center py-4 text-gray-500">Loading messages...</div>
                        ) : recentMessages.length === 0 ? (
                          <div className="text-center py-4 text-gray-500">No recent messages</div>
                        ) : (
                          recentMessages.map((message) => (
                          <div key={message.id} className="flex items-center justify-between p-3 border rounded-lg">
                            <div className="flex-1">
                              <div className="flex items-center gap-2">
                                <p className="font-medium">{message.name}</p>
                                {message.status === "unread" && (
                                  <Badge variant="secondary" className="text-xs">
                                    New
                                  </Badge>
                                )}
                              </div>
                              <p className="text-sm text-gray-600">{message.subject}</p>
                              <p className="text-xs text-gray-500">{new Date(message.createdAt).toLocaleDateString()}</p>
                            </div>
                            <Button variant="ghost" size="sm" onClick={() => handleMarkAsRead(message.id)}>
                              <Eye className="h-4 w-4" />
                            </Button>
                          </div>
                          ))
                        )}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Quick Actions */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Quick Actions</CardTitle>
                      <CardDescription>Common management tasks</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 gap-4">
                        <Button className="h-20 flex-col" asChild>
                          <Link href="/admin/dashboard/products">
                            <Plus className="h-6 w-6 mb-2" />
                            Manage Products
                          </Link>
                        </Button>
                        <Button variant="outline" className="h-20 flex-col bg-transparent" asChild>
                          <Link href="/admin/dashboard/projects">
                            <Plus className="h-6 w-6 mb-2" />
                            Manage Projects
                          </Link>
                        </Button>
                        <Button variant="outline" className="h-20 flex-col bg-transparent">
                          <MessageSquare className="h-6 w-6 mb-2" />
                          View Messages
                        </Button>
                        <Button variant="outline" className="h-20 flex-col bg-transparent">
                          <Download className="h-6 w-6 mb-2" />
                          Export Data
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="products" className="space-y-6">
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle>Product Management</CardTitle>
                        <CardDescription>Manage your construction materials catalog</CardDescription>
                      </div>
                      <Button asChild>
                        <Link href="/admin/dashboard/products">
                          <Plus className="h-4 w-4 mr-2" />
                          Manage Products
                        </Link>
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {isLoading ? (
                        <div className="text-center py-4 text-gray-500">Loading products...</div>
                      ) : recentProducts.length === 0 ? (
                        <div className="text-center py-4 text-gray-500">No products found</div>
                      ) : (
                        recentProducts.map((product) => (
                        <div key={product.id} className="flex items-center justify-between p-4 border rounded-lg">
                          <div>
                            <h3 className="font-medium">{product.name}</h3>
                            <p className="text-sm text-gray-600">{product.category}</p>
                            <p className="text-xs text-gray-500">
                              Updated: {new Date(product.updatedAt).toLocaleDateString()}
                            </p>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant={product.status === "active" ? "default" : "secondary"}>
                              {product.status}
                            </Badge>
                          </div>
                        </div>
                        ))
                      )}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="projects" className="space-y-6">
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle>Project Management</CardTitle>
                        <CardDescription>Manage your project portfolio</CardDescription>
                      </div>
                      <Button asChild>
                        <Link href="/admin/dashboard/projects">
                          <Plus className="h-4 w-4 mr-2" />
                          Manage Projects
                        </Link>
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {isLoading ? (
                        <div className="text-center py-4 text-gray-500">Loading projects...</div>
                      ) : recentProjects.length === 0 ? (
                        <div className="text-center py-4 text-gray-500">No projects found</div>
                      ) : (
                        recentProjects.map((project) => (
                        <div key={project.id} className="flex items-center justify-between p-4 border rounded-lg">
                          <div>
                            <h3 className="font-medium">{project.name}</h3>
                            <p className="text-sm text-gray-600">{project.type}</p>
                            <p className="text-xs text-gray-500">Date: {project.date}</p>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant={project.status === "completed" ? "default" : "secondary"}>
                              {project.status}
                            </Badge>
                          </div>
                        </div>
                        ))
                      )}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="messages" className="space-y-6">
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle>Contact Messages</CardTitle>
                        <CardDescription>View and manage contact form submissions</CardDescription>
                      </div>
                      <Button variant="outline">
                        <Download className="h-4 w-4 mr-2" />
                        Export CSV
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {isLoading ? (
                        <div className="text-center py-4 text-gray-500">Loading messages...</div>
                      ) : recentMessages.length === 0 ? (
                        <div className="text-center py-4 text-gray-500">No messages found</div>
                      ) : (
                        recentMessages.map((message) => (
                        <div key={message.id} className="p-4 border rounded-lg">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center gap-2">
                              <h3 className="font-medium">{message.name}</h3>
                              {message.status === "unread" && <Badge variant="secondary">New</Badge>}
                            </div>
                            <span className="text-sm text-gray-500">
                              {new Date(message.createdAt).toLocaleDateString()}
                            </span>
                          </div>
                          <p className="text-sm text-gray-600 mb-2">{message.email}</p>
                          <p className="text-sm font-medium mb-2">{message.subject}</p>
                          <p className="text-sm text-gray-700 mb-4">{message.message}</p>
                          <div className="flex gap-2">
                            <Button size="sm" variant="outline" onClick={() => handleMarkAsRead(message.id)}>
                              <Eye className="h-4 w-4 mr-2" />
                              {message.status === "unread" ? "Mark as Read" : "Read"}
                            </Button>
                            <Button size="sm" variant="outline">
                              Reply
                            </Button>
                          </div>
                        </div>
                        ))
                      )}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
        </div>
      </AuthGuard>
    </>
  )
}
