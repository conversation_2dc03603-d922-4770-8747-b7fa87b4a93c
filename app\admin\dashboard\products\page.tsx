"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/hooks/use-toast"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Plus, Edit, Trash2, Save, X, Upload, FileText, Image, Video, Settings, Search, Filter, ChevronDown, MoreHorizontal, Calendar, AlertCircle, Package } from "lucide-react"
import { db } from "@/lib/database"
import type { Product } from "@/lib/database"
import { AuthGuard } from "@/components/auth-guard"


export default function AdminProductsPage() {
  const [products, setProducts] = useState<Product[]>([])
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([])
  const [editingProduct, setEditingProduct] = useState<Product | null>(null)
  const [isCreating, setIsCreating] = useState(false)
  const { toast } = useToast()

  // Filter states
  const [filters, setFilters] = useState({
    productId: "",
    productName: "",
    category: "",
    status: "",
    fromDate: "",
    toDate: "",
  })
  
  // UI states
  const [showFilters, setShowFilters] = useState(true)
  const [selectedProducts, setSelectedProducts] = useState<number[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(10)
  const [sortField, setSortField] = useState<string>("updatedAt")
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc")

  // JSON-LD for Admin Products Page
  const adminProductsJsonLd = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "@id": "https://drylexiraq.com/admin/dashboard/products#products",
    "name": "Admin Products - DRYLEX Iraq",
    "description": "Admin dashboard for managing products on DRYLEX Iraq website. Add, edit, and remove construction materials and their details.",
    "url": "https://drylexiraq.com/admin/dashboard/products",
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "Admin Dashboard",
          "item": "https://drylexiraq.com/admin/dashboard"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "Products",
          "item": "https://drylexiraq.com/admin/dashboard/products"
        }
      ]
    },
    "mainContentOfPage": {
      "@type": "WebApplication",
      "name": "DRYLEX Iraq Admin Products",
      "description": "Web application for managing construction materials product catalog for DRYLEX Iraq website",
      "operatingSystem": "Web",
      "applicationCategory": "BusinessApplication",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD",
        "eligibleRegion": "IQ",
        "availability": "https://schema.org/InStock",
        "seller": {
          "@type": "Organization",
          "name": "DRYLEX Iraq",
          "url": "https://drylexiraq.com"
        }
      },
      "featureList": {
        "@type": "CategoryCodeSet",
        "name": "Product Management Features",
        "includesCategoryCode": [
          {
            "@type": "CategoryCode",
            "codeValue": "create",
            "name": "Create New Products"
          },
          {
            "@type": "CategoryCode",
            "codeValue": "edit",
            "name": "Edit Existing Products"
          },
          {
            "@type": "CategoryCode",
            "codeValue": "delete",
            "name": "Delete Products"
          },
          {
            "@type": "CategoryCode",
            "codeValue": "category",
            "name": "Manage Product Categories"
          }
        ]
      }
    },
    "potentialAction": {
      "@type": "Action",
      "name": "Manage Products",
      "description": "Add, edit, and delete construction material products in the catalog",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": "https://drylexiraq.com/admin/dashboard/products",
        "actionPlatform": [
          "https://schema.org/DesktopWebPlatform",
          "https://schema.org/MobileWebPlatform"
        ]
      }
    },
    "inLanguage": {
      "@type": "Language",
      "name": "English",
      "alternateName": "en"
    },
    "isPartOf": {
      "@type": "WebSite",
      "name": "DRYLEX Iraq Admin",
      "url": "https://drylexiraq.com/admin",
      "description": "Administrative dashboard for managing DRYLEX Iraq website content",
      "publisher": {
        "@type": "Organization",
        "name": "DRYLEX Iraq",
        "logo": {
          "@type": "ImageObject",
          "url": "https://drylexiraq.com/logo.png"
        },
        "url": "https://drylexiraq.com"
      }
    }
  }

  // JSON-LD for Organization
  const organizationJsonLd = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "DRYLEX IRAQ",
    "url": "https://drylexiraq.com",
    "logo": "https://drylexiraq.com/logo.png",
    "description": "Authorized distributor of Drylex products in Iraq providing high-performance construction materials and solutions",
    "foundingDate": "2015",
    "founders": [
      {
        "@type": "Person",
        "name": "Ali Al-Maliki"
      }
    ],
    "numberOfEmployees": "50",
    "industry": "Construction Materials",
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "Al-Muhandisin, Nasiriyah",
      "addressLocality": "Nasiriyah",
      "addressRegion": "Thi Qar Governorate",
      "postalCode": "64001",
      "addressCountry": "Iraq"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "+964 7812345678",
      "contactType": "customer service",
      "areaServed": "IQ",
      "availableLanguage": "en"
    },
    "sameAs": [
      "https://www.facebook.com/drylexiraq",
      "https://www.instagram.com/drylexiraq",
      "https://www.linkedin.com/company/drylex-iraq"
    ]
  }

  // JSON-LD for Product List
  const productListJsonLd = {
    "@context": "https://schema.org",
    "@type": "ItemList",
    "name": "Construction Materials Product Catalog",
    "description": "List of construction materials products managed through the DRYLEX IRAQ admin dashboard",
    "itemListElement": products.map((product, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "url": `https://drylexiraq.com/admin/dashboard/products/edit/${product.id}`,
      "item": {
        "@type": "Product",
        "name": product.name,
        "description": product.description,
        "category": product.category,
        "image": product.image,
        "offers": {
          "@type": "Offer",
          "price": "0",
          "priceCurrency": "USD",
          "availability": "https://schema.org/InStock",
          "seller": {
            "@type": "Organization",
            "name": "DRYLEX Iraq"
          }
        }
      }
    }))
  }


  const [formData, setFormData] = useState({
    name: "",
    nameAr: "",
    category: "",
    image: "",
    description: "",
    descriptionAr: "",
    features: "",
    pdfUrl: "",
    youtubeCode: "",
    status: "active" as "active" | "draft",
  })

  const [categories, setCategories] = useState<string[]>([])

  const [isManagingCategories, setIsManagingCategories] = useState(false)
  const [newCategory, setNewCategory] = useState("")
  const [uploadingImage, setUploadingImage] = useState(false)
  const [uploadingPdf, setUploadingPdf] = useState(false)

  useEffect(() => {
    loadProducts()
    // Load categories from backend
    ;(async () => {
      try {
        const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'https://localhost:56266/api'}/category`)
        if (res.ok) {
          const data = await res.json()
          const names = Array.isArray(data) ? data.map((c: any) => c.name).filter(Boolean) : []
          if (names.length) setCategories(names)
        }
      } catch {}
    })()
  }, [])

  useEffect(() => {
    applyFilters()
  }, [products, filters, sortField, sortOrder])

  const loadProducts = () => {
    setProducts(db.getAllProducts())
  }

  const applyFilters = () => {
    let filtered = [...products]

    // Apply filters
    if (filters.productId) {
      filtered = filtered.filter(p => p.id.toString().includes(filters.productId))
    }
    if (filters.productName) {
      filtered = filtered.filter(p => 
        p.name.toLowerCase().includes(filters.productName.toLowerCase()) ||
        (p.nameAr && p.nameAr.toLowerCase().includes(filters.productName.toLowerCase()))
      )
    }
    if (filters.category) {
      filtered = filtered.filter(p => p.category === filters.category)
    }
    if (filters.status) {
      filtered = filtered.filter(p => p.status === filters.status)
    }
    if (filters.fromDate) {
      const fromDate = new Date(filters.fromDate)
      filtered = filtered.filter(p => new Date(p.updatedAt) >= fromDate)
    }
    if (filters.toDate) {
      const toDate = new Date(filters.toDate)
      toDate.setHours(23, 59, 59, 999) // End of day
      filtered = filtered.filter(p => new Date(p.updatedAt) <= toDate)
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any = a[sortField as keyof Product]
      let bValue: any = b[sortField as keyof Product]

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase()
        bValue = bValue.toLowerCase()
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : aValue < bValue ? -1 : 0
      } else {
        return aValue < bValue ? 1 : aValue > bValue ? -1 : 0
      }
    })

    setFilteredProducts(filtered)
  }

  const resetFilters = () => {
    setFilters({
      productId: "",
      productName: "",
      category: "",
      status: "",
      fromDate: "",
      toDate: "",
    })
  }

  const handleCreate = () => {
    setIsCreating(true)
    setFormData({
      name: "",
      nameAr: "",
      category: "",
      image: "",
      description: "",
      descriptionAr: "",
      features: "",
      pdfUrl: "",
      youtubeCode: "",
      status: "active",
    })
  }

  const handleEdit = (product: Product) => {
    setEditingProduct(product)
    setFormData({
      name: product.name,
      nameAr: product.nameAr,
      category: product.category,
      image: product.image,
      description: product.description,
      descriptionAr: product.descriptionAr,
      features: product.features.join(", "),
      pdfUrl: product.pdfUrl,
      youtubeCode: (product as any).youtubeCode || "",
      status: product.status,
    })
  }

  const handleSave = () => {
    try {
      const productData = {
        ...formData,
        features: formData.features
          .split(",")
          .map((f) => f.trim())
          .filter((f) => f),
      }

      if (editingProduct) {
        db.updateProduct(editingProduct.id, productData)
        toast({ title: "Product updated successfully!" })
      } else {
        db.createProduct(productData)
        toast({ title: "Product created successfully!" })
      }

      loadProducts()
      handleCancel()
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save product",
        variant: "destructive",
      })
    }
  }

  const handleDelete = (id: number) => {
    if (confirm("Are you sure you want to delete this product?")) {
      db.deleteProduct(id)
      loadProducts()
      toast({ title: "Product deleted successfully!" })
    }
  }

  const handleCancel = () => {
    setEditingProduct(null)
    setIsCreating(false)
    setFormData({
      name: "",
      nameAr: "",
      category: "",
      image: "",
      description: "",
      descriptionAr: "",
      features: "",
      pdfUrl: "",
      youtubeCode: "",
      status: "active",
    })
  }

  // File upload handlers
  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setUploadingImage(true)
    try {
      const formData = new FormData()
      formData.append('file', file)
      const sessionRaw = typeof window !== 'undefined' ? sessionStorage.getItem('adminSession') : null
      const session = sessionRaw ? JSON.parse(sessionRaw) : null
      const token = session?.token

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'https://localhost:56266/api'}/fileupload/image`, {
        method: 'POST',
        // Do NOT set Content-Type when sending FormData
        headers: token ? { 'Authorization': `Bearer ${token}` } : undefined,
        body: formData
      })

      if (!response.ok) {
        throw new Error('Upload failed')
      }

      const result = await response.json()
      setFormData(prev => ({ ...prev, image: result.url }))
      setUploadingImage(false)
      toast({ title: "Image uploaded successfully!" })
    } catch (error) {
      setUploadingImage(false)
      toast({
        title: "Upload failed",
        description: "Failed to upload image",
        variant: "destructive",
      })
    }
  }

  const handlePdfUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setUploadingPdf(true)
    try {
      const formData = new FormData()
      formData.append('file', file)
      const sessionRaw = typeof window !== 'undefined' ? sessionStorage.getItem('adminSession') : null
      const session = sessionRaw ? JSON.parse(sessionRaw) : null
      const token = session?.token

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'https://localhost:56266/api'}/fileupload/pdf`, {
        method: 'POST',
        headers: token ? { 'Authorization': `Bearer ${token}` } : undefined,
        body: formData
      })

      if (!response.ok) {
        throw new Error('Upload failed')
      }

      const result = await response.json()
      setFormData(prev => ({ ...prev, pdfUrl: result.url }))
      setUploadingPdf(false)
      toast({ title: "PDF uploaded successfully!" })
    } catch (error) {
      setUploadingPdf(false)
      toast({
        title: "Upload failed",
        description: "Failed to upload PDF",
        variant: "destructive",
      })
    }
  }

  // Category management
  const handleAddCategory = async () => {
    if (!newCategory.trim() || categories.some(cat => cat.toLowerCase() === newCategory.trim().toLowerCase())) {
      return
    }

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'https://localhost:56266/api'}/category`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${sessionStorage.getItem('adminSession')}`
        },
        body: JSON.stringify({
          name: newCategory.trim()
        })
      })

      if (!response.ok) {
        throw new Error('Failed to create category')
      }

      const result = await response.json()
      setCategories([...categories, result.name])
      setNewCategory("")
      toast({ title: "Category added successfully!" })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add category",
        variant: "destructive",
      })
    }
  }

  const handleDeleteCategory = async (categoryToDelete: string) => {
    if (!confirm(`Are you sure you want to delete the category "${categoryToDelete}"?`)) {
      return
    }

    try {
      // Find category ID (this would need to be stored with categories)
      // For now, we'll just remove from local state
      setCategories(categories.filter(cat => cat !== categoryToDelete))
      toast({ title: "Category deleted successfully!" })
    } catch (error) {
      toast({
        title: "Error", 
        description: "Failed to delete category",
        variant: "destructive",
      })
    }
  }

  const extractYouTubeId = (url: string) => {
    const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/
    const match = url.match(regex)
    return match ? match[1] : url
  }

  // Pagination calculations
  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentProducts = filteredProducts.slice(startIndex, endIndex)

  return (
    <AuthGuard>
      <>
        <script 
          type="application/ld+json" 
          dangerouslySetInnerHTML={{ __html: JSON.stringify(adminProductsJsonLd) }} 
        />
        <script 
          type="application/ld+json" 
          dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationJsonLd) }} 
        />
        <script 
          type="application/ld+json" 
          dangerouslySetInnerHTML={{ __html: JSON.stringify(productListJsonLd) }} 
        />
        <div className="p-6">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
                  <Package className="h-8 w-8" />
                  Products List
                </h1>
                <p className="text-gray-600">Manage your construction materials catalog</p>
              </div>
              <div className="flex gap-3">
                <Button onClick={handleCreate} className="bg-blue-600 hover:bg-blue-700 text-white">
                  <Plus className="h-4 w-4 mr-2" />
                  Add New
                </Button>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="bg-teal-600 text-white border-teal-600 hover:bg-teal-700">
                      <Settings className="h-4 w-4 mr-2" />
                      Actions
                      <ChevronDown className="h-4 w-4 ml-2" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Product Actions</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => setIsManagingCategories(true)}>
                      <Settings className="h-4 w-4 mr-2" />
                      Manage Categories
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => {}}>
                      <Upload className="h-4 w-4 mr-2" />
                      Bulk Import
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => {}}>
                      <FileText className="h-4 w-4 mr-2" />
                      Export Products
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>

            {/* Search Filters Card */}
            <Card className="mb-6">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <Search className="h-5 w-5" />
                    Search
                  </CardTitle>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={() => setShowFilters(!showFilters)}
                  >
                    <Filter className="h-4 w-4 mr-2" />
                    {showFilters ? 'Hide Filters' : 'Show Filters'}
                  </Button>
                </div>
              </CardHeader>
              
              {showFilters && (
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                    {/* Product ID */}
                    <div className="space-y-2">
                      <Label htmlFor="productId" className="flex items-center gap-2">
                        Product Id: 
                        <AlertCircle className="h-3 w-3 text-blue-500" />
                      </Label>
                      <Input
                        id="productId"
                        placeholder="Product Id"
                        value={filters.productId}
                        onChange={(e) => setFilters({...filters, productId: e.target.value})}
                      />
                    </div>

                    {/* Product Name */}
                    <div className="space-y-2">
                      <Label htmlFor="productName" className="flex items-center gap-2">
                        Product Name: 
                        <AlertCircle className="h-3 w-3 text-blue-500" />
                      </Label>
                      <Input
                        id="productName"
                        placeholder="Product Name"
                        value={filters.productName}
                        onChange={(e) => setFilters({...filters, productName: e.target.value})}
                      />
                    </div>

                    {/* Category */}
                    <div className="space-y-2">
                      <Label htmlFor="category" className="flex items-center gap-2">
                        Category: 
                        <AlertCircle className="h-3 w-3 text-blue-500" />
                      </Label>
                      <Select
                        value={filters.category}
                        onValueChange={(value) => setFilters({...filters, category: value})}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select Category" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="">All Categories</SelectItem>
                          {categories.map((category) => (
                            <SelectItem key={category} value={category}>
                              {category}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Status */}
                    <div className="space-y-2">
                      <Label htmlFor="status" className="flex items-center gap-2">
                        Status: 
                        <AlertCircle className="h-3 w-3 text-blue-500" />
                      </Label>
                      <Select
                        value={filters.status}
                        onValueChange={(value) => setFilters({...filters, status: value})}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select Status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="">All Status</SelectItem>
                          <SelectItem value="active">Active</SelectItem>
                          <SelectItem value="draft">Draft</SelectItem>
                          <SelectItem value="inactive">Inactive</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Date Filters */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div className="space-y-2">
                      <Label htmlFor="fromDate" className="flex items-center gap-2">
                        From Date: 
                        <AlertCircle className="h-3 w-3 text-blue-500" />
                      </Label>
                      <div className="relative">
                        <Input
                          id="fromDate"
                          type="date"
                          placeholder="From Date ..."
                          value={filters.fromDate}
                          onChange={(e) => setFilters({...filters, fromDate: e.target.value})}
                          className="pl-10"
                        />
                        <Calendar className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="toDate" className="flex items-center gap-2">
                        To Date: 
                        <AlertCircle className="h-3 w-3 text-blue-500" />
                      </Label>
                      <div className="relative">
                        <Input
                          id="toDate"
                          type="date"
                          placeholder="To Date ..."
                          value={filters.toDate}
                          onChange={(e) => setFilters({...filters, toDate: e.target.value})}
                          className="pl-10"
                        />
                        <Calendar className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-4">
                    <Button onClick={resetFilters} variant="outline">
                      Reset
                    </Button>
                    <Button onClick={applyFilters} className="bg-blue-600 hover:bg-blue-700">
                      <Search className="h-4 w-4 mr-2" />
                      Search
                    </Button>
                  </div>
                </CardContent>
              )}
            </Card>

            {/* Products List Table */}
            <Card>
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <Package className="h-5 w-5" />
                    Products List
                  </CardTitle>
                  <Button variant="ghost" size="sm" onClick={() => setShowFilters(!showFilters)}>
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              
              <CardContent>
                {/* Table Controls */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <Label htmlFor="itemsPerPage">Show:</Label>
                    <Select
                      value={itemsPerPage.toString()}
                      onValueChange={(value) => {
                        setItemsPerPage(Number(value))
                        setCurrentPage(1)
                      }}
                    >
                      <SelectTrigger className="w-20">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="10">10</SelectItem>
                        <SelectItem value="25">25</SelectItem>
                        <SelectItem value="50">50</SelectItem>
                        <SelectItem value="100">100</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="text-sm text-gray-600">
                    Showing {startIndex + 1} to {Math.min(endIndex, filteredProducts.length)} of {filteredProducts.length} entries
                  </div>
                </div>

                {/* Data Table */}
                <div className="border rounded-lg overflow-hidden">
                  <Table>
                    <TableHeader className="bg-teal-600">
                      <TableRow>
                        <TableHead className="text-white font-semibold">Product ID</TableHead>
                        <TableHead className="text-white font-semibold flex items-center gap-2">
                          Picture <AlertCircle className="h-3 w-3 text-red-400" />
                        </TableHead>
                        <TableHead className="text-white font-semibold">Name</TableHead>
                        <TableHead className="text-white font-semibold">Price</TableHead>
                        <TableHead className="text-white font-semibold">Is Active</TableHead>
                        <TableHead className="text-white font-semibold">Stock Quantity</TableHead>
                        <TableHead className="text-white font-semibold">Created Date</TableHead>
                        <TableHead className="text-white font-semibold text-center">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {currentProducts.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={8} className="text-center py-8 text-gray-500">
                            No products found
                          </TableCell>
                        </TableRow>
                      ) : (
                        currentProducts.map((product, index) => (
                          <TableRow key={product.id} className={index % 2 === 0 ? "bg-gray-50" : "bg-white"}>
                            <TableCell className="font-medium">{product.id}</TableCell>
                            <TableCell>
                              {product.image ? (
                                <img 
                                  src={product.image} 
                                  alt={product.name}
                                  className="w-16 h-16 object-cover rounded border"
                                />
                              ) : (
                                <div className="w-16 h-16 bg-gray-200 rounded border flex items-center justify-center">
                                  <Image className="h-6 w-6 text-gray-400" />
                                </div>
                              )}
                            </TableCell>
                            <TableCell>
                              <div>
                                <div className="font-medium">{product.name}</div>
                                {product.nameAr && (
                                  <div className="text-sm text-gray-500">{product.nameAr}</div>
                                )}
                              </div>
                            </TableCell>
                            <TableCell>
                              {product.price ? (
                                <span className="font-medium">${product.price.toFixed(2)}</span>
                              ) : (
                                <span className="text-gray-400">$25.00</span>
                              )}
                            </TableCell>
                            <TableCell>
                              <Badge 
                                variant={product.status === "active" ? "default" : "secondary"}
                                className={product.status === "active" ? "bg-green-100 text-green-800" : ""}
                              >
                                {product.status === "active" ? "Active" : "Inactive"}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <span className="font-medium">100</span>
                            </TableCell>
                            <TableCell>
                              {new Date(product.updatedAt).toLocaleDateString('en-US', {
                                day: '2-digit',
                                month: 'short',
                                year: 'numeric'
                              })}
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <Button 
                                  variant="ghost" 
                                  size="sm"
                                  onClick={() => handleEdit(product)}
                                  className="text-blue-600 hover:text-blue-800 hover:bg-blue-50"
                                >
                                  <Edit className="h-4 w-4 mr-1" />
                                  Edit
                                </Button>
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                      <MoreHorizontal className="h-4 w-4" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuItem 
                                      onClick={() => handleDelete(product.id)}
                                      className="text-red-600"
                                    >
                                      <Trash2 className="h-4 w-4 mr-2" />
                                      Delete
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="flex items-center justify-between mt-4">
                    <div className="text-sm text-gray-600">
                      Page {currentPage} of {totalPages}
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                        disabled={currentPage === 1}
                      >
                        Previous
                      </Button>
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        const pageNumber = i + 1
                        return (
                          <Button
                            key={pageNumber}
                            variant={currentPage === pageNumber ? "default" : "outline"}
                            size="sm"
                            onClick={() => setCurrentPage(pageNumber)}
                          >
                            {pageNumber}
                          </Button>
                        )
                      })}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                        disabled={currentPage === totalPages}
                      >
                        Next
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Create/Edit Product Dialog */}
            {(isCreating || editingProduct) && (
            <Card className="mb-8">
              <CardHeader>
                <CardTitle>{editingProduct ? "Edit Product" : "Create New Product"}</CardTitle>
                <CardDescription>
                  {editingProduct ? "Update product information" : "Add a new product to your catalog"}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Product Name (English)</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      placeholder="Enter product name"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="nameAr">Product Name (Arabic)</Label>
                    <Input
                      id="nameAr"
                      value={formData.nameAr}
                      onChange={(e) => setFormData({ ...formData, nameAr: e.target.value })}
                      placeholder="Enter Arabic name"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="category">Category</Label>
                    <Select
                      value={formData.category}
                      onValueChange={(value) => setFormData({ ...formData, category: value })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category} value={category}>
                            {category}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="status">Status</Label>
                    <Select
                      value={formData.status}
                      onValueChange={(value: "active" | "draft") => setFormData({ ...formData, status: value })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="draft">Draft</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="image">Product Image</Label>
                  <div className="flex gap-4">
                    <div className="flex-1">
                      <Input
                        id="image"
                        value={formData.image}
                        onChange={(e) => setFormData({ ...formData, image: e.target.value })}
                        placeholder="Enter image URL or upload file"
                      />
                    </div>
                    <div className="relative">
                      <input
                        type="file"
                        id="imageUpload"
                        accept="image/*"
                        onChange={handleImageUpload}
                        className="absolute inset-0 opacity-0 cursor-pointer"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        disabled={uploadingImage}
                        className="w-full"
                      >
                        {uploadingImage ? (
                          <>Uploading...</>
                        ) : (
                          <>
                            <Image className="h-4 w-4 mr-2" />
                            Upload
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                  {formData.image && (
                    <div className="mt-2">
                      <img 
                        src={formData.image} 
                        alt="Preview" 
                        className="w-32 h-32 object-cover rounded border"
                      />
                    </div>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="description">Description (English)</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      placeholder="Enter product description"
                      rows={3}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="descriptionAr">Description (Arabic)</Label>
                    <Textarea
                      id="descriptionAr"
                      value={formData.descriptionAr}
                      onChange={(e) => setFormData({ ...formData, descriptionAr: e.target.value })}
                      placeholder="Enter Arabic description"
                      rows={3}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="features">Features (comma-separated)</Label>
                    <Textarea
                      id="features"
                      value={formData.features}
                      onChange={(e) => setFormData({ ...formData, features: e.target.value })}
                      placeholder="Feature 1, Feature 2, Feature 3"
                      rows={3}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="youtubeCode">YouTube Video</Label>
                    <Input
                      id="youtubeCode"
                      value={formData.youtubeCode}
                      onChange={(e) => setFormData({ ...formData, youtubeCode: e.target.value })}
                      placeholder="YouTube URL or video ID"
                    />
                    {formData.youtubeCode && (
                      <div className="mt-2">
                        <iframe
                          width="100%"
                          height="200"
                          src={`https://www.youtube.com/embed/${extractYouTubeId(formData.youtubeCode)}`}
                          frameBorder="0"
                          allowFullScreen
                          className="rounded border"
                        />
                      </div>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="pdfUrl">PDF Data Sheet</Label>
                  <div className="flex gap-4">
                    <div className="flex-1">
                      <Input
                        id="pdfUrl"
                        value={formData.pdfUrl}
                        onChange={(e) => setFormData({ ...formData, pdfUrl: e.target.value })}
                        placeholder="Enter PDF URL or upload file"
                      />
                    </div>
                    <div className="relative">
                      <input
                        type="file"
                        id="pdfUpload"
                        accept=".pdf"
                        onChange={handlePdfUpload}
                        className="absolute inset-0 opacity-0 cursor-pointer"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        disabled={uploadingPdf}
                        className="w-full"
                      >
                        {uploadingPdf ? (
                          <>Uploading...</>
                        ) : (
                          <>
                            <FileText className="h-4 w-4 mr-2" />
                            Upload PDF
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                  {formData.pdfUrl && (
                    <div className="mt-2">
                      <a
                        href={formData.pdfUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 underline text-sm"
                      >
                        View PDF Data Sheet
                      </a>
                    </div>
                  )}
                </div>

                <div className="flex gap-4">
                  <Button onClick={handleSave} className="bg-green-600 hover:bg-green-700">
                    <Save className="h-4 w-4 mr-2" />
                    Save Product
                  </Button>
                  <Button onClick={handleCancel} variant="outline">
                    <X className="h-4 w-4 mr-2" />
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Category Management Modal */}
          {isManagingCategories && (
            <Card className="mb-8">
              <CardHeader>
                <CardTitle>Manage Categories</CardTitle>
                <CardDescription>
                  Add, edit, or remove product categories
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex gap-4">
                  <div className="flex-1">
                    <Input
                      value={newCategory}
                      onChange={(e) => setNewCategory(e.target.value)}
                      placeholder="Enter new category name"
                      onKeyDown={(e) => e.key === 'Enter' && handleAddCategory()}
                    />
                  </div>
                  <Button onClick={handleAddCategory} className="bg-green-600 hover:bg-green-700">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Category
                  </Button>
                </div>

                <div className="space-y-2">
                  <Label>Current Categories</Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                    {categories.map((category) => (
                      <div key={category} className="flex items-center justify-between p-3 border rounded-lg">
                        <span className="font-medium">{category}</span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteCategory(category)}
                          className="text-red-600 hover:text-red-800 hover:bg-red-50"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="flex gap-4">
                  <Button onClick={() => setIsManagingCategories(false)} variant="outline">
                    <X className="h-4 w-4 mr-2" />
                    Close
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

        </div>
      </>
    </AuthGuard>
  )
}

