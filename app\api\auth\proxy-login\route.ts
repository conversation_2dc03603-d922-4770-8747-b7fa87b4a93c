import { NextRequest, NextResponse } from "next/server"
import https from 'https'

// Create a custom fetch function that handles self-signed certificates
async function fetchWithSSLBypass(url: string, options: any) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const postData = options.body;

    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port,
      path: urlObj.pathname,
      method: options.method,
      headers: options.headers,
      rejectUnauthorized: false // This bypasses SSL certificate validation
    };

    const req = https.request(requestOptions, (res) => {
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        const response = {
          ok: res.statusCode >= 200 && res.statusCode < 300,
          status: res.statusCode,
          statusText: res.statusMessage,
          headers: new Map(Object.entries(res.headers)),
          json: async () => JSON.parse(data),
          text: async () => data
        };
        resolve(response);
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (postData) {
      req.write(postData);
    }

    req.end();
  });
}

// Proxy to backend API to avoid CORS issues
export async function POST(request: NextRequest) {
  console.log('=== Proxy login API called ===');
  console.log('Request headers:', Object.fromEntries(request.headers.entries()));

  try {
    const body = await request.json();
    console.log('Request body received:', { email: body.email, password: body.password ? '***' : 'missing' });

    console.log('Attempting to connect to backend at https://localhost:56266/api/auth/login');

    // Forward the request to the actual backend using custom SSL bypass
    const backendResponse = await fetchWithSSLBypass('https://localhost:56266/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'NextJS-Proxy/1.0',
        'Content-Length': Buffer.byteLength(JSON.stringify(body))
      },
      body: JSON.stringify(body)
    }) as any;
    
    console.log('Backend response received:');
    console.log('- Status:', backendResponse.status);
    console.log('- Status Text:', backendResponse.statusText);
    console.log('- Headers:', Object.fromEntries(backendResponse.headers.entries()));

    if (backendResponse.ok) {
      const data = await backendResponse.json();
      console.log('Backend response data:', data);
      console.log('✅ Backend authentication successful');

      return NextResponse.json(data);
    } else {
      let errorData;
      const contentType = backendResponse.headers.get('content-type');

      if (contentType && contentType.includes('application/json')) {
        errorData = await backendResponse.json();
      } else {
        errorData = { message: await backendResponse.text() };
      }

      console.log('❌ Backend error response:', errorData);
      console.log('Backend error status:', backendResponse.status);

      return NextResponse.json(
        { success: false, message: errorData.message || 'Backend authentication failed' },
        { status: backendResponse.status }
      );
    }
    
  } catch (error) {
    console.error("=== Proxy login error ===");
    console.error("Error object:", error);
    console.error("Error type:", typeof error);
    console.error("Error constructor:", error?.constructor?.name);

    if (error instanceof Error) {
      console.error("Error message:", error.message);
      console.error("Error stack:", error.stack);
      console.error("Error name:", error.name);

      // Detailed error analysis
      if (error.message.includes('ECONNREFUSED')) {
        console.error("❌ Connection refused - Backend server not running");
        return NextResponse.json(
          { success: false, message: 'Backend server is not running on https://localhost:56266. Please start your backend server.' },
          { status: 503 }
        );
      }

      if (error.message.includes('ENOTFOUND') || error.message.includes('getaddrinfo')) {
        console.error("❌ DNS resolution failed");
        return NextResponse.json(
          { success: false, message: 'Cannot resolve localhost:56266. Check if the backend server is running.' },
          { status: 503 }
        );
      }

      if (error.message.includes('certificate') || error.message.includes('SSL') || error.message.includes('TLS')) {
        console.error("❌ SSL/TLS certificate error");
        return NextResponse.json(
          { success: false, message: 'SSL certificate error. Backend using self-signed certificate that cannot be verified.' },
          { status: 503 }
        );
      }

      if (error.message.includes('CORS')) {
        console.error("❌ CORS error");
        return NextResponse.json(
          { success: false, message: 'CORS error: Backend not configured to accept requests from this origin.' },
          { status: 403 }
        );
      }

      if (error.message.includes('timeout') || error.message.includes('ETIMEDOUT')) {
        console.error("❌ Request timeout");
        return NextResponse.json(
          { success: false, message: 'Request timeout: Backend server not responding.' },
          { status: 504 }
        );
      }
    }

    console.error("❌ Unknown proxy error");
    return NextResponse.json(
      { success: false, message: 'Proxy request failed: ' + (error instanceof Error ? error.message : 'Unknown error') },
      { status: 500 }
    );
  }
}
