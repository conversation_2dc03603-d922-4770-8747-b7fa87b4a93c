import { NextRequest, NextResponse } from "next/server"

// Disable SSL verification for development
process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';

// Test endpoint to verify backend connectivity
export async function GET(request: NextRequest) {
  console.log('=== Testing backend connectivity ===');
  
  try {
    console.log('Attempting to connect to https://localhost:56266...');
    
    // Try HTTP first, then HTTPS
    let response;
    let url = 'http://localhost:56266/health';

    try {
      console.log('Trying HTTP connection...');
      response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'NextJS-Test/1.0'
        }
      });
    } catch (httpError) {
      console.log('HTTP failed, trying HTTPS...');
      url = 'https://localhost:56266/health';
      response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'NextJS-Test/1.0'
        }
      });
    }

    console.log(`Backend health check response from ${url}:`, response.status);
    
    if (response.ok) {
      const data = await response.text();
      return NextResponse.json({
        success: true,
        message: 'Backend is accessible',
        status: response.status,
        data: data
      });
    } else {
      return NextResponse.json({
        success: false,
        message: 'Backend responded but with error',
        status: response.status,
        statusText: response.statusText
      });
    }
    
  } catch (error) {
    console.error('Backend connectivity test failed:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Cannot connect to backend',
      error: error instanceof Error ? error.message : 'Unknown error',
      details: {
        errorType: error?.constructor?.name,
        errorMessage: error instanceof Error ? error.message : undefined
      }
    });
  }
}
