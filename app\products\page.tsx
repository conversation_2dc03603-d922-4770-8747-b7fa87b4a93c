"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Download, Search, MessageCircle, Play, FileText } from "lucide-react"
import Image from "next/image"
import { AnimatedSection } from "@/components/animated-section"
import { StaggerContainer } from "@/components/stagger-container"
import { AnimatedCard } from "@/components/animated-card"
import { db } from "@/lib/database"
import type { Product } from "@/lib/database"
import Head from "next/head"

// Get settings with default values
const settings = db.getSettings() || { address: '', phone: '', email: '' }

// JSON-LD for Products Page
const productsJsonLd = {
  "@context": "https://schema.org",
  "@type": "CollectionPage",
  name: "Construction Materials Products - DRYLEX Iraq",
  description:
    "DRYLEX Iraq construction materials including concrete admixtures, waterproofing solutions, repair materials and construction chemicals.",
  url: "https://drylexiraq.com/products",
  mainEntity: {
    "@type": "ItemList",
    name: "Construction Materials",
    numberOfItems: 50,
    itemListElement: [
      {
        "@type": "Product",
        name: "Concrete Admixtures",
        description: "High-performance concrete admixtures for enhanced workability and strength",
      },
      {
        "@type": "Product",
        name: "Waterproofing Solutions",
        description: "Premium waterproofing membranes and liquid systems",
      },
      {
        "@type": "Product",
        name: "Repair Materials",
        description: "Advanced concrete repair and injection materials",
      },
    ],
  },
}

export default function ProductsPage() {
  const products = db.getProducts()

  const extractYouTubeId = (url: string) => {
    const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/
    const match = url.match(regex)
    return match ? match[1] : url
  }
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('All')
  
  // Filter products based on search term and selected category
  const filteredProducts = products.filter((product) => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'All' || product.category.includes(selectedCategory)
    return matchesSearch && matchesCategory
  })
  
  const categories = ['All', 'Drylex', 'Waterproofing', 'Concrete', 'Repair', 'Chemicals']

  // JSON-LD for Product
  const productJsonLd = {
    "@context": "https://schema.org",
    "@type": "Product",
    name: "Drylex Waterproofing Systems",
    description: "Premium waterproofing membranes and liquid systems for Iraqi construction projects",
    brand: {
      "@type": "Brand",
      name: "Drylex",
    },
    category: "Construction Materials > Waterproofing",
    image: "https://drylexiraq.com/images/products/waterproofing-system.jpg",
    offers: {
      "@type": "OfferCatalog",
      name: "Waterproofing Systems Catalog",
      url: "https://drylexiraq.com/products/waterproofing",
      numberOfItems: "20",
    },
    aggregateRating: {
      "@type": "AggregateRating",
      ratingValue: "4.7",
      reviewCount: "85",
    },
    manufacturer: {
      "@type": "Organization",
      name: "Drylex",
      url: "https://drylex.com",
      logo: "https://drylex.com/logo.png",
    },
    review: [
      {
        "@type": "Review",
        reviewRating: {
          "@type": "Rating",
          ratingValue: "5",
          bestRating: "5",
        },
        author: "Ali Al-Maliki",
        datePublished: "2024-08-15",
        name: "Excellent waterproofing solution",
        reviewBody: "Used Drylex waterproofing system for a basement project in Baghdad. No leaks after 6 months, even during heavy rains. Highly recommended!"
      },
      {
        "@type": "Review",
        reviewRating: {
          "@type": "Rating",
          ratingValue: "5",
          bestRating: "5",
        },
        author: "Ahmed Al-Sudani",
        datePublished: "2024-07-20",
        name: "Superior quality materials",
        reviewBody: "Drylex construction chemicals have exceeded our expectations in several infrastructure projects. Durable and reliable solutions."
      },
    ]
  }

  // JSON-LD for Organization
  const organizationJsonLd = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "DRYLEX IRAQ",
    "url": "https://drylexiraq.com",
    "logo": "https://drylexiraq.com/logo.png",
    "description": "Authorized distributor of Drylex products in Iraq providing high-performance construction materials and solutions",
    "foundingDate": "2015",
    "founders": [
      {
        "@type": "Person",
        "name": "Ali Al-Maliki"
      }
    ],
    "numberOfEmployees": "50",
    "industry": "Construction Materials",
    "address": {
      "@type": "PostalAddress",
      "streetAddress": settings.address,
      "addressLocality": "Nasiriyah",
      "addressRegion": "Thi Qar Governorate",
      "postalCode": "64001",
      "addressCountry": "Iraq"
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": settings.phone,
      "contactType": "customer service",
      "areaServed": "IQ",
      "availableLanguage": "en"
    },
    "sameAs": [
      "https://facebook.com/drylexiraq",
      "https://instagram.com/drylexiraq",
      "https://linkedin.com/company/drylexiraq"
    ]
  }

  return (
    <>
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(productJsonLd) }} />
      <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationJsonLd) }} />
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="container mx-auto px-4">
          {/* Header */}
          <AnimatedSection className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">Our Construction Materials</h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Professional-grade construction materials for every project need. Trusted by contractors across IRAQ since
              1995.
            </p>
          </AnimatedSection>

          {/* Search and Filter */}
          <AnimatedSection className="mb-8 flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search construction materials..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
                aria-label="Search products"
              />
            </div>
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <Button
                  key={category}
                  variant={selectedCategory === category ? "default" : "outline"}
                  onClick={() => setSelectedCategory(category)}
                  className="text-sm"
                  aria-label={`Filter by ${category}`}
                >
                  {category}
                </Button>
              ))}
            </div>
          </AnimatedSection>

          {/* Products Grid */}
          <StaggerContainer className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredProducts.map((product, index) => (
              <AnimatedCard key={product.id} delay={index * 0.1}>
                <Card className="group hover:shadow-lg transition-shadow h-full">
                  <div className="relative overflow-hidden rounded-t-lg">
                    <Image
                      src={product.image || "/placeholder.svg"}
                      alt={`${product.name} - ${product.category} by DRYLEX  IRAQ`}
                      width={400}
                      height={300}
                      className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                      loading={index < 6 ? "eager" : "lazy"}
                    />
                    <Badge className="absolute top-4 left-4 bg-orange-600">{product.category}</Badge>
                  </div>
                  <CardHeader>
                    <CardTitle className="text-xl">
                      {product.name}
                      <span className="block text-sm text-gray-500 font-normal mt-1">{product.nameAr}</span>
                    </CardTitle>
                    <CardDescription>{product.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex flex-wrap gap-2">
                        {product.features.map((feature, idx) => (
                          <Badge key={idx} variant="outline" className="text-xs">
                            {feature}
                          </Badge>
                        ))}
                      </div>
                      <div className="flex flex-wrap gap-2">
                        {/* PDF Download Button */}
                        {product.pdfUrl && (
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex-1 bg-transparent min-w-[100px]"
                            onClick={() => window.open(product.pdfUrl, "_blank")}
                            aria-label={`Download ${product.name} datasheet`}
                          >
                            <FileText className="h-4 w-4 mr-2" />
                            PDF
                          </Button>
                        )}
                        
                        {/* YouTube Video Button */}
                        {(product as any).youtubeCode && (
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex-1 bg-transparent min-w-[100px] text-red-600 border-red-600 hover:bg-red-50"
                            onClick={() => window.open(`https://www.youtube.com/watch?v=${extractYouTubeId((product as any).youtubeCode)}`, '_blank')}
                            aria-label={`Watch video for ${product.name}`}
                          >
                            <Play className="h-4 w-4 mr-2" />
                            Video
                          </Button>
                        )}
                        
                        <Button
                          size="sm"
                          className="flex-1 bg-green-600 hover:bg-green-700 min-w-[100px]"
                          onClick={() => handleRequestQuote(product.name)}
                          aria-label={`Request quote for ${product.name}`}
                        >
                          <MessageCircle className="h-4 w-4 mr-2" />
                          Quote
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </AnimatedCard>
            ))}
          </StaggerContainer>

          {filteredProducts.length === 0 && (
            <AnimatedSection className="text-center py-12">
              <p className="text-gray-500 text-lg">No construction materials found matching your criteria.</p>
            </AnimatedSection>
          )}
        </div>
      </div>
    </>
  )
}
