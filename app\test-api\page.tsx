"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

export default function ApiTestPage() {
  const [results, setResults] = useState<any>({})
  const [loading, setLoading] = useState(false)

  const testEndpoint = async (name: string, url: string, needsAuth = false) => {
    setLoading(true)
    try {
      const headers: any = {
        'Content-Type': 'application/json',
      }

      if (needsAuth) {
        const sessionRaw = sessionStorage.getItem('adminSession')
        if (sessionRaw) {
          const session = JSON.parse(sessionRaw)
          if (session.token) {
            headers['Authorization'] = `Bearer ${session.token}`
          }
        }
      }

      const response = await fetch(url, {
        method: 'GET',
        headers
      })

      const result = {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
        data: null,
        error: null
      }

      if (response.ok) {
        try {
          result.data = await response.json()
        } catch (e) {
          result.data = 'Response is not JSON'
        }
      } else {
        try {
          result.error = await response.json()
        } catch (e) {
          result.error = 'Error response is not JSON'
        }
      }

      setResults(prev => ({ ...prev, [name]: result }))
    } catch (error: any) {
      setResults(prev => ({ 
        ...prev, 
        [name]: { 
          status: 0, 
          statusText: 'Network Error', 
          ok: false, 
          data: null, 
          error: error.message 
        }
      }))
    }
    setLoading(false)
  }

  const testAllEndpoints = async () => {
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'https://localhost:56266/api'
    
    await testEndpoint('Products', `${baseUrl}/products?pageSize=1`)
    await testEndpoint('Categories', `${baseUrl}/category`)
    await testEndpoint('Projects', `${baseUrl}/projects?pageSize=1`)
    await testEndpoint('Services', `${baseUrl}/services?pageSize=1`)
    await testEndpoint('Contact', `${baseUrl}/contact`)
    await testEndpoint('Categories Admin', `${baseUrl}/category/admin`, true)
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">API Connection Test</h1>
          <p className="text-gray-600">Test backend API endpoints to diagnose connection issues</p>
        </div>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle>API Configuration</CardTitle>
            <CardDescription>Current API settings</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p><strong>Base URL:</strong> {process.env.NEXT_PUBLIC_API_URL || 'https://localhost:56266/api'}</p>
              <p><strong>Environment:</strong> {process.env.NODE_ENV}</p>
              <p><strong>Current Origin:</strong> {typeof window !== 'undefined' ? window.location.origin : 'N/A'}</p>
            </div>
          </CardContent>
        </Card>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Test Controls</CardTitle>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={testAllEndpoints} 
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {loading ? 'Testing...' : 'Test All Endpoints'}
            </Button>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {Object.entries(results).map(([name, result]: [string, any]) => (
            <Card key={name}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{name}</CardTitle>
                  <Badge 
                    variant={result.ok ? "default" : "destructive"}
                    className={result.ok ? "bg-green-100 text-green-800" : ""}
                  >
                    {result.status} {result.statusText}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">Response:</h4>
                    <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40">
                      {JSON.stringify(result.data || result.error, null, 2)}
                    </pre>
                  </div>
                  <div className="text-sm text-gray-600">
                    <p><strong>Status:</strong> {result.status}</p>
                    <p><strong>OK:</strong> {result.ok ? 'Yes' : 'No'}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {Object.keys(results).length === 0 && (
          <Card>
            <CardContent className="text-center py-8">
              <p className="text-gray-500">Click "Test All Endpoints" to start testing API connections</p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
