using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using ConstructionWebsite.API.Data;
using ConstructionWebsite.API.Models;
using ConstructionWebsite.API.DTOs;
using System.ComponentModel.DataAnnotations;

namespace ConstructionWebsite.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class CategoryController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<CategoryController> _logger;

        public CategoryController(ApplicationDbContext context, ILogger<CategoryController> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// Get all product categories
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<CategoryDto>>> GetCategories()
        {
            try
            {
                var categories = await _context.ProductCategories
                    .Where(c => c.IsActive)
                    .OrderBy(c => c.SortOrder)
                    .ThenBy(c => c.Name)
                    .Select(c => new CategoryDto
                    {
                        Id = c.Id,
                        Name = c.Name,
                        NameAr = c.NameAr,
                        Slug = c.Slug,
                        Description = c.Description,
                        DescriptionAr = c.DescriptionAr,
                        Image = c.Image,
                        Icon = c.Icon,
                        SortOrder = c.SortOrder,
                        IsActive = c.IsActive,
                        ProductCount = c.Products.Count(p => p.Status == "active"),
                        CreatedAt = c.CreatedAt,
                        UpdatedAt = c.UpdatedAt
                    })
                    .ToListAsync();

                return Ok(categories);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving categories");
                return StatusCode(500, new { message = "An error occurred while retrieving categories" });
            }
        }

        /// <summary>
        /// Get category by ID
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<CategoryDto>> GetCategory(int id)
        {
            try
            {
                var category = await _context.ProductCategories
                    .Where(c => c.Id == id && c.IsActive)
                    .Select(c => new CategoryDto
                    {
                        Id = c.Id,
                        Name = c.Name,
                        NameAr = c.NameAr,
                        Slug = c.Slug,
                        Description = c.Description,
                        DescriptionAr = c.DescriptionAr,
                        Image = c.Image,
                        Icon = c.Icon,
                        SortOrder = c.SortOrder,
                        IsActive = c.IsActive,
                        ProductCount = c.Products.Count(p => p.Status == "active"),
                        CreatedAt = c.CreatedAt,
                        UpdatedAt = c.UpdatedAt
                    })
                    .FirstOrDefaultAsync();

                if (category == null)
                {
                    return NotFound(new { message = "Category not found" });
                }

                return Ok(category);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving category with ID {CategoryId}", id);
                return StatusCode(500, new { message = "An error occurred while retrieving the category" });
            }
        }

        /// <summary>
        /// Create a new category (Admin only)
        /// </summary>
        [HttpPost]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<CategoryDto>> CreateCategory([FromBody] CreateCategoryDto createCategoryDto)
        {
            try
            {
                // Check if category with same name already exists
                var existingCategory = await _context.ProductCategories
                    .AnyAsync(c => c.Name.ToLower() == createCategoryDto.Name.ToLower());

                if (existingCategory)
                {
                    return BadRequest(new { message = "A category with this name already exists" });
                }

                // Generate slug from name
                var slug = GenerateSlug(createCategoryDto.Name);
                
                // Ensure slug is unique
                var slugExists = await _context.ProductCategories.AnyAsync(c => c.Slug == slug);
                if (slugExists)
                {
                    slug = $"{slug}-{DateTime.UtcNow.Ticks}";
                }

                var category = new ProductCategory
                {
                    Name = createCategoryDto.Name,
                    NameAr = createCategoryDto.NameAr,
                    Slug = slug,
                    Description = createCategoryDto.Description,
                    DescriptionAr = createCategoryDto.DescriptionAr,
                    Image = createCategoryDto.Image,
                    Icon = createCategoryDto.Icon,
                    SortOrder = createCategoryDto.SortOrder ?? 0,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.ProductCategories.Add(category);
                await _context.SaveChangesAsync();

                var categoryDto = new CategoryDto
                {
                    Id = category.Id,
                    Name = category.Name,
                    NameAr = category.NameAr,
                    Slug = category.Slug,
                    Description = category.Description,
                    DescriptionAr = category.DescriptionAr,
                    Image = category.Image,
                    Icon = category.Icon,
                    SortOrder = category.SortOrder,
                    IsActive = category.IsActive,
                    ProductCount = 0,
                    CreatedAt = category.CreatedAt,
                    UpdatedAt = category.UpdatedAt
                };

                return CreatedAtAction(nameof(GetCategory), new { id = category.Id }, categoryDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating category");
                return StatusCode(500, new { message = "An error occurred while creating the category" });
            }
        }

        /// <summary>
        /// Update category (Admin only)
        /// </summary>
        [HttpPut("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<CategoryDto>> UpdateCategory(int id, [FromBody] UpdateCategoryDto updateCategoryDto)
        {
            try
            {
                var category = await _context.ProductCategories.FindAsync(id);
                if (category == null)
                {
                    return NotFound(new { message = "Category not found" });
                }

                // Check if category with same name already exists (excluding current category)
                var existingCategory = await _context.ProductCategories
                    .AnyAsync(c => c.Name.ToLower() == updateCategoryDto.Name.ToLower() && c.Id != id);

                if (existingCategory)
                {
                    return BadRequest(new { message = "A category with this name already exists" });
                }

                // Update fields
                category.Name = updateCategoryDto.Name;
                category.NameAr = updateCategoryDto.NameAr;
                category.Description = updateCategoryDto.Description;
                category.DescriptionAr = updateCategoryDto.DescriptionAr;
                category.Image = updateCategoryDto.Image;
                category.Icon = updateCategoryDto.Icon;
                category.SortOrder = updateCategoryDto.SortOrder ?? category.SortOrder;
                category.IsActive = updateCategoryDto.IsActive ?? category.IsActive;
                category.UpdatedAt = DateTime.UtcNow;

                // Update slug if name changed
                if (updateCategoryDto.Name != category.Name)
                {
                    var newSlug = GenerateSlug(updateCategoryDto.Name);
                    var slugExists = await _context.ProductCategories.AnyAsync(c => c.Slug == newSlug && c.Id != id);
                    if (slugExists)
                    {
                        newSlug = $"{newSlug}-{DateTime.UtcNow.Ticks}";
                    }
                    category.Slug = newSlug;
                }

                await _context.SaveChangesAsync();

                var categoryDto = new CategoryDto
                {
                    Id = category.Id,
                    Name = category.Name,
                    NameAr = category.NameAr,
                    Slug = category.Slug,
                    Description = category.Description,
                    DescriptionAr = category.DescriptionAr,
                    Image = category.Image,
                    Icon = category.Icon,
                    SortOrder = category.SortOrder,
                    IsActive = category.IsActive,
                    ProductCount = await _context.Products.CountAsync(p => p.CategoryId == category.Id && p.Status == "active"),
                    CreatedAt = category.CreatedAt,
                    UpdatedAt = category.UpdatedAt
                };

                return Ok(categoryDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating category with ID {CategoryId}", id);
                return StatusCode(500, new { message = "An error occurred while updating the category" });
            }
        }

        /// <summary>
        /// Delete category (Admin only)
        /// </summary>
        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> DeleteCategory(int id)
        {
            try
            {
                var category = await _context.ProductCategories.FindAsync(id);
                if (category == null)
                {
                    return NotFound(new { message = "Category not found" });
                }

                // Check if category has products
                var hasProducts = await _context.Products.AnyAsync(p => p.CategoryId == id);
                if (hasProducts)
                {
                    return BadRequest(new { message = "Cannot delete category that has products assigned to it" });
                }

                _context.ProductCategories.Remove(category);
                await _context.SaveChangesAsync();

                return Ok(new { message = "Category deleted successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting category with ID {CategoryId}", id);
                return StatusCode(500, new { message = "An error occurred while deleting the category" });
            }
        }

        /// <summary>
        /// Get categories with product counts (Admin only)
        /// </summary>
        [HttpGet("admin")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<IEnumerable<CategoryDto>>> GetCategoriesForAdmin()
        {
            try
            {
                var categories = await _context.ProductCategories
                    .OrderBy(c => c.SortOrder)
                    .ThenBy(c => c.Name)
                    .Select(c => new CategoryDto
                    {
                        Id = c.Id,
                        Name = c.Name,
                        NameAr = c.NameAr,
                        Slug = c.Slug,
                        Description = c.Description,
                        DescriptionAr = c.DescriptionAr,
                        Image = c.Image,
                        Icon = c.Icon,
                        SortOrder = c.SortOrder,
                        IsActive = c.IsActive,
                        ProductCount = c.Products.Count(),
                        CreatedAt = c.CreatedAt,
                        UpdatedAt = c.UpdatedAt
                    })
                    .ToListAsync();

                return Ok(categories);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving categories for admin");
                return StatusCode(500, new { message = "An error occurred while retrieving categories" });
            }
        }

        private static string GenerateSlug(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            return input.ToLower()
                .Replace(" ", "-")
                .Replace("&", "and")
                .Replace(".", "")
                .Replace(",", "")
                .Replace("'", "")
                .Replace("\"", "")
                .Replace("(", "")
                .Replace(")", "")
                .Replace("[", "")
                .Replace("]", "")
                .Replace("{", "")
                .Replace("}", "")
                .Replace("/", "-")
                .Replace("\\", "-")
                .Replace("_", "-")
                .Replace("--", "-")
                .Trim('-');
        }
    }

    // DTOs for Category operations
    public class CreateCategoryDto
    {
        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;

        [StringLength(200)]
        public string? NameAr { get; set; }

        [StringLength(500)]
        public string? Description { get; set; }

        [StringLength(500)]
        public string? DescriptionAr { get; set; }

        [StringLength(500)]
        public string? Image { get; set; }

        [StringLength(100)]
        public string? Icon { get; set; }

        public int? SortOrder { get; set; }
    }

    public class UpdateCategoryDto
    {
        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;

        [StringLength(200)]
        public string? NameAr { get; set; }

        [StringLength(500)]
        public string? Description { get; set; }

        [StringLength(500)]
        public string? DescriptionAr { get; set; }

        [StringLength(500)]
        public string? Image { get; set; }

        [StringLength(100)]
        public string? Icon { get; set; }

        public int? SortOrder { get; set; }

        public bool? IsActive { get; set; }
    }
}
