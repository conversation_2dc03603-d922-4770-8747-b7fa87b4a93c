using Microsoft.AspNetCore.Mvc;
using ConstructionWebsite.API.Services;
using ConstructionWebsite.API.DTOs;

namespace ConstructionWebsite.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ContactController : ControllerBase
    {
        private readonly IContactService _contactService;
        private readonly ILogger<ContactController> _logger;

        public ContactController(IContactService contactService, ILogger<ContactController> logger)
        {
            _contactService = contactService;
            _logger = logger;
        }

        /// <summary>
        /// Get all contact messages
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<ContactDto>>> GetContactMessages()
        {
            try
            {
                var messages = await _contactService.GetAllContactMessagesAsync();
                
                var contactDtos = messages.Select(m => new ContactDto
                {
                    Id = m.Id,
                    Name = m.Name,
                    Email = m.Email,
                    Phone = m.Phone,
                    Company = m.Company,
                    Subject = m.Subject,
                    Message = m.Message,
                    InquiryType = m.InquiryType,
                    IsRead = m.IsRead,
                    Source = m.Source,
                    IpAddress = m.IpAddress,
                    UserAgent = m.UserAgent,
                    CreatedAt = m.CreatedAt,
                    UpdatedAt = m.UpdatedAt
                }).ToList();

                return Ok(contactDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving contact messages");
                return StatusCode(500, new { message = "An error occurred while retrieving contact messages" });
            }
        }

        /// <summary>
        /// Mark contact message as read
        /// </summary>
        [HttpPatch("{id}/mark-read")]
        public async Task<ActionResult> MarkAsRead(int id)
        {
            try
            {
                var success = await _contactService.MarkAsReadAsync(id);
                
                if (!success)
                {
                    return NotFound(new { message = "Contact message not found" });
                }

                return Ok(new { message = "Contact message marked as read" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking contact message as read for ID {ContactMessageId}", id);
                return StatusCode(500, new { message = "An error occurred while marking the message as read" });
            }
        }
    }
}