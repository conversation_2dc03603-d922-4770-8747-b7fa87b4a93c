using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using ConstructionWebsite.API.Services;
using ConstructionWebsite.API.DTOs;
using System.ComponentModel.DataAnnotations;

namespace ConstructionWebsite.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Roles = "Admin")]
    public class FileUploadController : ControllerBase
    {
        private readonly IFileUploadService _fileUploadService;
        private readonly ILogger<FileUploadController> _logger;
        private readonly IConfiguration _configuration;

        public FileUploadController(
            IFileUploadService fileUploadService, 
            ILogger<FileUploadController> logger,
            IConfiguration configuration)
        {
            _fileUploadService = fileUploadService;
            _logger = logger;
            _configuration = configuration;
        }

        /// <summary>
        /// Upload image file (Admin only)
        /// </summary>
        [HttpPost("image")]
        public async Task<ActionResult<FileUploadResponse>> UploadImage(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest(new { message = "No file was uploaded" });
                }

                // Validate file type
                var allowedTypes = new[] { "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp" };
                if (!allowedTypes.Contains(file.ContentType.ToLower()))
                {
                    return BadRequest(new { message = "Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed." });
                }

                // Validate file size (5MB max)
                var maxSizeBytes = 5 * 1024 * 1024; // 5MB
                if (file.Length > maxSizeBytes)
                {
                    return BadRequest(new { message = "File size too large. Maximum size is 5MB." });
                }

                var result = await _fileUploadService.UploadImageAsync(file, "products");

                return Ok(new FileUploadResponse
                {
                    Success = true,
                    Url = result.Url,
                    FileName = result.FileName,
                    FileSize = result.FileSize,
                    ContentType = result.ContentType,
                    Message = "Image uploaded successfully"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading image file");
                return StatusCode(500, new { message = "An error occurred while uploading the image" });
            }
        }

        /// <summary>
        /// Upload PDF file (Admin only)
        /// </summary>
        [HttpPost("pdf")]
        public async Task<ActionResult<FileUploadResponse>> UploadPdf(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return BadRequest(new { message = "No file was uploaded" });
                }

                // Validate file type
                if (!file.ContentType.ToLower().Equals("application/pdf"))
                {
                    return BadRequest(new { message = "Invalid file type. Only PDF files are allowed." });
                }

                // Validate file size (10MB max for PDFs)
                var maxSizeBytes = 10 * 1024 * 1024; // 10MB
                if (file.Length > maxSizeBytes)
                {
                    return BadRequest(new { message = "File size too large. Maximum size is 10MB." });
                }

                var result = await _fileUploadService.UploadPdfAsync(file, "datasheets");

                return Ok(new FileUploadResponse
                {
                    Success = true,
                    Url = result.Url,
                    FileName = result.FileName,
                    FileSize = result.FileSize,
                    ContentType = result.ContentType,
                    Message = "PDF uploaded successfully"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading PDF file");
                return StatusCode(500, new { message = "An error occurred while uploading the PDF" });
            }
        }

        /// <summary>
        /// Upload multiple images (Admin only)
        /// </summary>
        [HttpPost("images/multiple")]
        public async Task<ActionResult<MultipleFileUploadResponse>> UploadMultipleImages(List<IFormFile> files)
        {
            try
            {
                if (files == null || files.Count == 0)
                {
                    return BadRequest(new { message = "No files were uploaded" });
                }

                if (files.Count > 10)
                {
                    return BadRequest(new { message = "Too many files. Maximum 10 files allowed per upload." });
                }

                var results = new List<FileUploadResult>();
                var errors = new List<string>();

                foreach (var file in files)
                {
                    try
                    {
                        // Validate file type
                        var allowedTypes = new[] { "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp" };
                        if (!allowedTypes.Contains(file.ContentType.ToLower()))
                        {
                            errors.Add($"{file.FileName}: Invalid file type");
                            continue;
                        }

                        // Validate file size
                        var maxSizeBytes = 5 * 1024 * 1024; // 5MB
                        if (file.Length > maxSizeBytes)
                        {
                            errors.Add($"{file.FileName}: File size too large");
                            continue;
                        }

                        var result = await _fileUploadService.UploadImageAsync(file, "products");
                        results.Add(result);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error uploading file {FileName}", file.FileName);
                        errors.Add($"{file.FileName}: Upload failed");
                    }
                }

                return Ok(new MultipleFileUploadResponse
                {
                    Success = results.Count > 0,
                    SuccessCount = results.Count,
                    ErrorCount = errors.Count,
                    Files = results,
                    Errors = errors,
                    Message = $"Uploaded {results.Count} files successfully" + (errors.Count > 0 ? $", {errors.Count} failed" : "")
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading multiple images");
                return StatusCode(500, new { message = "An error occurred while uploading images" });
            }
        }

        /// <summary>
        /// Delete uploaded file (Admin only)
        /// </summary>
        [HttpDelete]
        public async Task<IActionResult> DeleteFile([FromQuery] string url)
        {
            try
            {
                if (string.IsNullOrEmpty(url))
                {
                    return BadRequest(new { message = "File URL is required" });
                }

                await _fileUploadService.DeleteFileAsync(url);

                return Ok(new { message = "File deleted successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting file with URL {FileUrl}", url);
                return StatusCode(500, new { message = "An error occurred while deleting the file" });
            }
        }

        /// <summary>
        /// Get upload configuration
        /// </summary>
        [HttpGet("config")]
        public ActionResult<FileUploadConfig> GetUploadConfig()
        {
            var config = new FileUploadConfig
            {
                MaxImageSizeMB = 5,
                MaxPdfSizeMB = 10,
                AllowedImageTypes = new[] { "image/jpeg", "image/png", "image/gif", "image/webp" },
                AllowedPdfTypes = new[] { "application/pdf" },
                MaxFilesPerUpload = 10
            };

            return Ok(config);
        }
    }

    // DTOs for File Upload operations
    public class FileUploadResponse
    {
        public bool Success { get; set; }
        public string Url { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public string ContentType { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
    }

    public class MultipleFileUploadResponse
    {
        public bool Success { get; set; }
        public int SuccessCount { get; set; }
        public int ErrorCount { get; set; }
        public List<FileUploadResult> Files { get; set; } = new();
        public List<string> Errors { get; set; } = new();
        public string Message { get; set; } = string.Empty;
    }


    public class FileUploadConfig
    {
        public int MaxImageSizeMB { get; set; }
        public int MaxPdfSizeMB { get; set; }
        public string[] AllowedImageTypes { get; set; } = Array.Empty<string>();
        public string[] AllowedPdfTypes { get; set; } = Array.Empty<string>();
        public int MaxFilesPerUpload { get; set; }
    }
}
