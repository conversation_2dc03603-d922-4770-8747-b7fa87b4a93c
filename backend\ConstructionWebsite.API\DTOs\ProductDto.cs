namespace ConstructionWebsite.API.DTOs
{
    public class ProductDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Slug { get; set; } = string.Empty;
        public string? ShortDescription { get; set; }
        public decimal? Price { get; set; }
        public string Currency { get; set; } = "USD";
        public string? Unit { get; set; }
        public string? Brand { get; set; }
        public bool IsFeatured { get; set; }
        public bool IsBestseller { get; set; }
        public bool IsNew { get; set; }
        public int ViewCount { get; set; }
        public int InquiryCount { get; set; }
        public CategoryDto? Category { get; set; }
        public ImageDto? PrimaryImage { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    public class ProductDetailDto : ProductDto
    {
        public string? Description { get; set; }
        public Dictionary<string, object>? TechnicalSpecs { get; set; }
        public List<string>? Features { get; set; }
        public List<string>? Applications { get; set; }
        public List<string>? Benefits { get; set; }
        public string? UsageInstructions { get; set; }
        public string? SafetyInfo { get; set; }
        public string? StorageConditions { get; set; }
        public Dictionary<string, object>? PackagingInfo { get; set; }
        public string? CoverageArea { get; set; }
        public string? ShelfLife { get; set; }
        public List<string>? Certifications { get; set; }
        public int MinOrderQty { get; set; }
        public decimal? Weight { get; set; }
        public string? Dimensions { get; set; }
        public string? Color { get; set; }
        public string? OriginCountry { get; set; }
        public string? Barcode { get; set; }
        public string? QRCode { get; set; }
        public string? PdfDatasheet { get; set; }
        public string? VideoUrl { get; set; }
        public string? YoutubeCode { get; set; }
        public string? SeoTitle { get; set; }
        public string? SeoDescription { get; set; }
        public string? SeoKeywords { get; set; }
        public List<ImageDto> Images { get; set; } = new List<ImageDto>();
        public DateTime? UpdatedAt { get; set; }
    }

    public class CreateProductDto
    {
        public int? CategoryId { get; set; }
        public string SKU { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string? NameAr { get; set; }
        public string Slug { get; set; } = string.Empty;
        public string? ShortDescription { get; set; }
        public string? ShortDescriptionAr { get; set; }
        public string? Description { get; set; }
        public string? DescriptionAr { get; set; }
        public decimal? Price { get; set; }
        public string Currency { get; set; } = "USD";
        public int MinOrderQty { get; set; } = 1;
        public string? Unit { get; set; }
        public decimal? Weight { get; set; }
        public string? Brand { get; set; }
        public string Status { get; set; } = "active";
        public bool IsFeatured { get; set; }
        public bool IsBestseller { get; set; }
        public bool IsNew { get; set; }
        public int SortOrder { get; set; }
        public string? PdfDatasheet { get; set; }
        public string? VideoUrl { get; set; }
        public string? YoutubeCode { get; set; }
    }

    public class UpdateProductDto
    {
        public int? CategoryId { get; set; }
        public string? SKU { get; set; }
        public string? Name { get; set; }
        public string? NameAr { get; set; }
        public string? Slug { get; set; }
        public string? ShortDescription { get; set; }
        public string? ShortDescriptionAr { get; set; }
        public string? Description { get; set; }
        public string? DescriptionAr { get; set; }
        public decimal? Price { get; set; }
        public string? Currency { get; set; }
        public int? MinOrderQty { get; set; }
        public string? Unit { get; set; }
        public decimal? Weight { get; set; }
        public string? Brand { get; set; }
        public string? Status { get; set; }
        public bool? IsFeatured { get; set; }
        public bool? IsBestseller { get; set; }
        public bool? IsNew { get; set; }
        public int? SortOrder { get; set; }
        public string? PdfDatasheet { get; set; }
        public string? VideoUrl { get; set; }
        public string? YoutubeCode { get; set; }
    }
}