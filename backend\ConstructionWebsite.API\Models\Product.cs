using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ConstructionWebsite.API.Models
{
    public class Product
    {
        [Key]
        public int Id { get; set; }

        [ForeignKey("Category")]
        public int? CategoryId { get; set; }

        [StringLength(50)]
        public string? SKU { get; set; }

        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;

        [StringLength(200)]
        public string? NameAr { get; set; }

        [Required]
        [StringLength(200)]
        public string Slug { get; set; } = string.Empty;

        [StringLength(500)]
        public string? ShortDescription { get; set; }

        [StringLength(500)]
        public string? ShortDescriptionAr { get; set; }

        [Column(TypeName = "ntext")]
        public string? Description { get; set; }

        [Column(TypeName = "ntext")]
        public string? DescriptionAr { get; set; }

        [Column(TypeName = "ntext")]
        public string? TechnicalSpecs { get; set; }

        [Column(TypeName = "ntext")]
        public string? TechnicalSpecsAr { get; set; }

        [Column(TypeName = "ntext")]
        public string? Features { get; set; }

        [Column(TypeName = "ntext")]
        public string? FeaturesAr { get; set; }

        [Column(TypeName = "ntext")]
        public string? Applications { get; set; }

        [Column(TypeName = "ntext")]
        public string? ApplicationsAr { get; set; }

        [Column(TypeName = "ntext")]
        public string? Benefits { get; set; }

        [Column(TypeName = "ntext")]
        public string? BenefitsAr { get; set; }

        [Column(TypeName = "ntext")]
        public string? UsageInstructions { get; set; }

        [Column(TypeName = "ntext")]
        public string? UsageInstructionsAr { get; set; }

        [Column(TypeName = "ntext")]
        public string? SafetyInfo { get; set; }

        [Column(TypeName = "ntext")]
        public string? SafetyInfoAr { get; set; }

        [Column(TypeName = "ntext")]
        public string? StorageConditions { get; set; }

        [Column(TypeName = "ntext")]
        public string? StorageConditionsAr { get; set; }

        [Column(TypeName = "ntext")]
        public string? PackagingInfo { get; set; }

        [StringLength(100)]
        public string? CoverageArea { get; set; }

        [StringLength(50)]
        public string? ShelfLife { get; set; }

        [Column(TypeName = "ntext")]
        public string? Certifications { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? Price { get; set; }

        [StringLength(3)]
        public string Currency { get; set; } = "AED";

        public int MinOrderQty { get; set; } = 1;

        [StringLength(20)]
        public string? Unit { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? Weight { get; set; }

        [StringLength(100)]
        public string? Dimensions { get; set; }

        [StringLength(50)]
        public string? Color { get; set; }

        [StringLength(100)]
        public string? Brand { get; set; }

        [StringLength(100)]
        public string? OriginCountry { get; set; }

        [StringLength(100)]
        public string? Barcode { get; set; }

        [StringLength(100)]
        public string? QRCode { get; set; }

        [StringLength(500)]
        public string? PdfDatasheet { get; set; }

        [StringLength(500)]
        public string? VideoUrl { get; set; }

        [StringLength(200)]
        public string? YoutubeCode { get; set; }

        [StringLength(200)]
        public string? SeoTitle { get; set; }

        [StringLength(200)]
        public string? SeoTitleAr { get; set; }

        [StringLength(500)]
        public string? SeoDescription { get; set; }

        [StringLength(500)]
        public string? SeoDescriptionAr { get; set; }

        [StringLength(500)]
        public string? SeoKeywords { get; set; }

        [StringLength(500)]
        public string? SeoKeywordsAr { get; set; }

        [Required]
        [StringLength(20)]
        public string Status { get; set; } = "active";

        public bool IsFeatured { get; set; } = false;

        public bool IsBestseller { get; set; } = false;

        public bool IsNew { get; set; } = false;

        public int ViewCount { get; set; } = 0;

        public int InquiryCount { get; set; } = 0;

        public int SortOrder { get; set; } = 0;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual ProductCategory? Category { get; set; }
        public virtual ICollection<ProductImage> Images { get; set; } = new List<ProductImage>();
        public virtual ICollection<ProductInquiry> Inquiries { get; set; } = new List<ProductInquiry>();
        public virtual ICollection<WishlistItem> WishlistItems { get; set; } = new List<WishlistItem>();
    }

    public class ProductCategory
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;

        [StringLength(200)]
        public string? NameAr { get; set; }

        [Required]
        [StringLength(200)]
        public string Slug { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Description { get; set; }

        [StringLength(500)]
        public string? DescriptionAr { get; set; }

        [StringLength(500)]
        public string? Image { get; set; }

        [StringLength(100)]
        public string? Icon { get; set; }

        public int SortOrder { get; set; } = 0;

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual ICollection<Product> Products { get; set; } = new List<Product>();
    }

    public class ProductImage
    {
        [Key]
        public int Id { get; set; }

        [ForeignKey("Product")]
        public int ProductId { get; set; }

        [Required]
        [StringLength(500)]
        public string ImageUrl { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string ImageType { get; set; } = string.Empty;

        [StringLength(200)]
        public string? AltText { get; set; }

        [StringLength(200)]
        public string? AltTextAr { get; set; }

        [StringLength(500)]
        public string? Caption { get; set; }

        [StringLength(500)]
        public string? CaptionAr { get; set; }

        public int? Width { get; set; }

        public int? Height { get; set; }

        public long? FileSize { get; set; }

        public int SortOrder { get; set; } = 0;

        public bool IsPrimary { get; set; } = false;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual Product Product { get; set; } = null!;
    }

    public class ProductInquiry
    {
        [Key]
        public int Id { get; set; }

        [ForeignKey("Product")]
        public int ProductId { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string Email { get; set; } = string.Empty;

        [StringLength(20)]
        public string? Phone { get; set; }

        [StringLength(200)]
        public string? Company { get; set; }

        [Required]
        [StringLength(200)]
        public string Subject { get; set; } = string.Empty;

        [Required]
        [Column(TypeName = "ntext")]
        public string Message { get; set; } = string.Empty;

        [StringLength(50)]
        public string InquiryType { get; set; } = "general";

        [StringLength(50)]
        public string Source { get; set; } = "website";

        [StringLength(45)]
        public string? IpAddress { get; set; }

        [StringLength(500)]
        public string? UserAgent { get; set; }

        [StringLength(100)]
        public string? UtmSource { get; set; }

        [StringLength(100)]
        public string? UtmMedium { get; set; }

        [StringLength(100)]
        public string? UtmCampaign { get; set; }

        [StringLength(20)]
        public string Status { get; set; } = "unread";

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual Product Product { get; set; } = null!;
    }

    public class WishlistItem
    {
        [Key]
        public int Id { get; set; }

        [ForeignKey("Product")]
        public int ProductId { get; set; }

        [StringLength(100)]
        public string? SessionId { get; set; }

        [StringLength(450)]
        public string? UserId { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation Properties
        public virtual Product Product { get; set; } = null!;
        public virtual ApplicationUser? User { get; set; }
    }
}
