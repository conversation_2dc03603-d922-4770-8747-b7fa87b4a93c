namespace ConstructionWebsite.API.Services
{
    public class FileUploadService : IFileUploadService
    {
        private readonly IWebHostEnvironment _environment;
        private readonly ILogger<FileUploadService> _logger;
        private readonly IConfiguration _configuration;
        private readonly string _uploadsPath;
        private readonly long _maxFileSize;
        private readonly string[] _allowedExtensions;

        public FileUploadService(
            IWebHostEnvironment environment,
            ILogger<FileUploadService> logger,
            IConfiguration configuration)
        {
            _environment = environment;
            _logger = logger;
            _configuration = configuration;
            
            _uploadsPath = Path.Combine(_environment.WebRootPath ?? _environment.ContentRootPath, "uploads");
            _maxFileSize = _configuration.GetValue<long>("FileUpload:MaxFileSizeInBytes", 10 * 1024 * 1024); // 10MB default
            _allowedExtensions = _configuration.GetSection("FileUpload:AllowedExtensions").Get<string[]>() 
                ?? new[] { ".jpg", ".jpeg", ".png", ".gif", ".pdf", ".doc", ".docx", ".xls", ".xlsx" };

            // Ensure uploads directory exists
            if (!Directory.Exists(_uploadsPath))
            {
                Directory.CreateDirectory(_uploadsPath);
            }
        }

        public async Task<string> UploadFileAsync(IFormFile file, string folder = "uploads")
        {
            try
            {
                if (file == null || file.Length == 0)
                    throw new ArgumentException("File is empty or null");

                if (!IsValidFileSize(file, _maxFileSize))
                    throw new ArgumentException($"File size exceeds maximum allowed size of {_maxFileSize / (1024 * 1024)}MB");

                if (!IsValidFileType(file, _allowedExtensions))
                    throw new ArgumentException($"File type not allowed. Allowed types: {string.Join(", ", _allowedExtensions)}");

                var folderPath = Path.Combine(_uploadsPath, folder);
                if (!Directory.Exists(folderPath))
                {
                    Directory.CreateDirectory(folderPath);
                }

                var fileName = GenerateUniqueFileName(file.FileName);
                var filePath = Path.Combine(folderPath, fileName);

                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                var relativePath = Path.Combine(folder, fileName).Replace('\\', '/');
                _logger.LogInformation("File uploaded successfully: {FilePath}", relativePath);
                
                return relativePath;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading file: {FileName}", file?.FileName);
                throw;
            }
        }

        public async Task<IEnumerable<string>> UploadMultipleFilesAsync(IEnumerable<IFormFile> files, string folder = "uploads")
        {
            var uploadedFiles = new List<string>();
            
            try
            {
                foreach (var file in files)
                {
                    var filePath = await UploadFileAsync(file, folder);
                    uploadedFiles.Add(filePath);
                }
                
                return uploadedFiles;
            }
            catch (Exception ex)
            {
                // Clean up any successfully uploaded files if an error occurs
                foreach (var uploadedFile in uploadedFiles)
                {
                    try
                    {
                        await DeleteFileAsync(uploadedFile);
                    }
                    catch (Exception cleanupEx)
                    {
                        _logger.LogWarning(cleanupEx, "Failed to clean up file during error recovery: {FilePath}", uploadedFile);
                    }
                }
                
                _logger.LogError(ex, "Error uploading multiple files");
                throw;
            }
        }

        public async Task<bool> DeleteFileAsync(string filePath)
        {
            try
            {
                var fullPath = Path.Combine(_uploadsPath, filePath.Replace('/', '\\'));
                
                if (File.Exists(fullPath))
                {
                    await Task.Run(() => File.Delete(fullPath));
                    _logger.LogInformation("File deleted successfully: {FilePath}", filePath);
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting file: {FilePath}", filePath);
                throw;
            }
        }

        public async Task<bool> FileExistsAsync(string filePath)
        {
            try
            {
                var fullPath = Path.Combine(_uploadsPath, filePath.Replace('/', '\\'));
                return await Task.FromResult(File.Exists(fullPath));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if file exists: {FilePath}", filePath);
                return false;
            }
        }

        public string GetFileUrl(string filePath)
        {
            return $"/uploads/{filePath.Replace('\\', '/')}".Replace("//", "/");
        }

        public async Task<FileUploadResult> UploadImageAsync(IFormFile file, string folder = "images")
        {
            try
            {
                if (file == null || file.Length == 0)
                    throw new ArgumentException("File is empty or null");

                // Validate image file types
                var allowedImageTypes = new[] { ".jpg", ".jpeg", ".png", ".gif", ".webp" };
                if (!IsValidFileType(file, allowedImageTypes))
                    throw new ArgumentException($"Invalid image file type. Allowed types: {string.Join(", ", allowedImageTypes)}");

                // Validate file size (5MB max for images)
                var maxImageSize = 5 * 1024 * 1024; // 5MB
                if (!IsValidFileSize(file, maxImageSize))
                    throw new ArgumentException($"Image file size exceeds maximum allowed size of {maxImageSize / (1024 * 1024)}MB");

                var folderPath = Path.Combine(_uploadsPath, folder);
                if (!Directory.Exists(folderPath))
                {
                    Directory.CreateDirectory(folderPath);
                }

                var fileName = GenerateUniqueFileName(file.FileName);
                var filePath = Path.Combine(folderPath, fileName);

                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                var relativePath = Path.Combine(folder, fileName).Replace('\\', '/');
                var url = GetFileUrl(relativePath);

                _logger.LogInformation("Image uploaded successfully: {FilePath}", relativePath);

                return new FileUploadResult
                {
                    Url = url,
                    FileName = fileName,
                    FileSize = file.Length,
                    ContentType = file.ContentType
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading image file: {FileName}", file?.FileName);
                throw;
            }
        }

        public async Task<FileUploadResult> UploadPdfAsync(IFormFile file, string folder = "pdfs")
        {
            try
            {
                if (file == null || file.Length == 0)
                    throw new ArgumentException("File is empty or null");

                // Validate PDF file type
                var allowedPdfTypes = new[] { ".pdf" };
                if (!IsValidFileType(file, allowedPdfTypes))
                    throw new ArgumentException("Invalid file type. Only PDF files are allowed.");

                // Validate file size (10MB max for PDFs)
                var maxPdfSize = 10 * 1024 * 1024; // 10MB
                if (!IsValidFileSize(file, maxPdfSize))
                    throw new ArgumentException($"PDF file size exceeds maximum allowed size of {maxPdfSize / (1024 * 1024)}MB");

                var folderPath = Path.Combine(_uploadsPath, folder);
                if (!Directory.Exists(folderPath))
                {
                    Directory.CreateDirectory(folderPath);
                }

                var fileName = GenerateUniqueFileName(file.FileName);
                var filePath = Path.Combine(folderPath, fileName);

                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                var relativePath = Path.Combine(folder, fileName).Replace('\\', '/');
                var url = GetFileUrl(relativePath);

                _logger.LogInformation("PDF uploaded successfully: {FilePath}", relativePath);

                return new FileUploadResult
                {
                    Url = url,
                    FileName = fileName,
                    FileSize = file.Length,
                    ContentType = file.ContentType
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading PDF file: {FileName}", file?.FileName);
                throw;
            }
        }

        public bool IsValidFileType(IFormFile file, string[] allowedExtensions)
        {
            if (file == null || string.IsNullOrEmpty(file.FileName))
                return false;

            var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
            return allowedExtensions.Contains(extension);
        }

        public bool IsValidFileSize(IFormFile file, long maxSizeInBytes)
        {
            return file != null && file.Length > 0 && file.Length <= maxSizeInBytes;
        }

        public async Task<byte[]> GetFileContentAsync(string filePath)
        {
            try
            {
                var fullPath = Path.Combine(_uploadsPath, filePath.Replace('/', '\\'));
                
                if (!File.Exists(fullPath))
                    throw new FileNotFoundException($"File not found: {filePath}");

                return await File.ReadAllBytesAsync(fullPath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reading file content: {FilePath}", filePath);
                throw;
            }
        }

        private string GenerateUniqueFileName(string originalFileName)
        {
            var extension = Path.GetExtension(originalFileName);
            var nameWithoutExtension = Path.GetFileNameWithoutExtension(originalFileName);
            var timestamp = DateTime.UtcNow.ToString("yyyyMMddHHmmss");
            var guid = Guid.NewGuid().ToString("N")[..8];
            
            return $"{nameWithoutExtension}_{timestamp}_{guid}{extension}";
        }
    }
}