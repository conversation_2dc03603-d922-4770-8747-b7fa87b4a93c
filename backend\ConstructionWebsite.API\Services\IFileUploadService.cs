namespace ConstructionWebsite.API.Services
{
    public interface IFileUploadService
    {
        Task<string> UploadFileAsync(IFormFile file, string folder = "uploads");
        Task<FileUploadResult> UploadImageAsync(IFormFile file, string folder = "images");
        Task<FileUploadResult> UploadPdfAsync(IFormFile file, string folder = "pdfs");
        Task<IEnumerable<string>> UploadMultipleFilesAsync(IEnumerable<IFormFile> files, string folder = "uploads");
        Task<bool> DeleteFileAsync(string filePath);
        Task<bool> FileExistsAsync(string filePath);
        string GetFileUrl(string filePath);
        bool IsValidFileType(IFormFile file, string[] allowedExtensions);
        bool IsValidFileSize(IFormFile file, long maxSizeInBytes);
        Task<byte[]> GetFileContentAsync(string filePath);
    }

    public class FileUploadResult
    {
        public string Url { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public string ContentType { get; set; } = string.Empty;
    }
}