[{"ContainingType": "ConstructionWebsite.API.Controllers.AuthController", "Method": "ChangePassword", "RelativePath": "api/Auth/change-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "ConstructionWebsite.API.Services.ChangePasswordModel", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ConstructionWebsite.API.Controllers.AuthController", "Method": "ForgotPassword", "RelativePath": "api/Auth/forgot-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "ConstructionWebsite.API.Controllers.ForgotPasswordRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ConstructionWebsite.API.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "ConstructionWebsite.API.Services.LoginModel", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ConstructionWebsite.API.Controllers.AuthController", "Method": "Logout", "RelativePath": "api/Auth/logout", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "ConstructionWebsite.API.Controllers.AuthController", "Method": "GetCurrentUser", "RelativePath": "api/Auth/me", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "ConstructionWebsite.API.Controllers.AuthController", "Method": "Register", "RelativePath": "api/Auth/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "ConstructionWebsite.API.Services.RegisterModel", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ConstructionWebsite.API.Controllers.AuthController", "Method": "ResetPassword", "RelativePath": "api/Auth/reset-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "ConstructionWebsite.API.Services.ResetPasswordModel", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ConstructionWebsite.API.Controllers.CategoryController", "Method": "GetCategories", "RelativePath": "api/Category", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[ConstructionWebsite.API.DTOs.CategoryDto, ConstructionWebsite.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ConstructionWebsite.API.Controllers.CategoryController", "Method": "CreateCategory", "RelativePath": "api/Category", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createCategoryDto", "Type": "ConstructionWebsite.API.Controllers.CreateCategoryDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ConstructionWebsite.API.DTOs.CategoryDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ConstructionWebsite.API.Controllers.CategoryController", "Method": "GetCategory", "RelativePath": "api/Category/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ConstructionWebsite.API.DTOs.CategoryDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ConstructionWebsite.API.Controllers.CategoryController", "Method": "UpdateCategory", "RelativePath": "api/Category/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "updateCategoryDto", "Type": "ConstructionWebsite.API.Controllers.UpdateCategoryDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ConstructionWebsite.API.DTOs.CategoryDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ConstructionWebsite.API.Controllers.CategoryController", "Method": "DeleteCategory", "RelativePath": "api/Category/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ConstructionWebsite.API.Controllers.CategoryController", "Method": "GetCategoriesForAdmin", "RelativePath": "api/Category/admin", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[ConstructionWebsite.API.DTOs.CategoryDto, ConstructionWebsite.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ConstructionWebsite.API.Controllers.ContactController", "Method": "GetContactMessages", "RelativePath": "api/Contact", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[ConstructionWebsite.API.DTOs.ContactDto, ConstructionWebsite.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ConstructionWebsite.API.Controllers.ContactController", "Method": "MarkAsRead", "RelativePath": "api/Contact/{id}/mark-read", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ConstructionWebsite.API.Controllers.FileUploadController", "Method": "DeleteFile", "RelativePath": "api/FileUpload", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "url", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "ConstructionWebsite.API.Controllers.FileUploadController", "Method": "GetUploadConfig", "RelativePath": "api/FileUpload/config", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ConstructionWebsite.API.Controllers.FileUploadConfig", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ConstructionWebsite.API.Controllers.FileUploadController", "Method": "UploadImage", "RelativePath": "api/FileUpload/image", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": [{"Type": "ConstructionWebsite.API.Controllers.FileUploadResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ConstructionWebsite.API.Controllers.FileUploadController", "Method": "UploadMultipleImages", "RelativePath": "api/FileUpload/images/multiple", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "files", "Type": "System.Collections.Generic.List`1[[Microsoft.AspNetCore.Http.IFormFile, Microsoft.AspNetCore.Http.Features, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60]]", "IsRequired": false}], "ReturnTypes": [{"Type": "ConstructionWebsite.API.Controllers.MultipleFileUploadResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ConstructionWebsite.API.Controllers.FileUploadController", "Method": "UploadPdf", "RelativePath": "api/FileUpload/pdf", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": [{"Type": "ConstructionWebsite.API.Controllers.FileUploadResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ConstructionWebsite.API.Controllers.ProductsController", "Method": "GetProducts", "RelativePath": "api/Products", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "search", "Type": "System.String", "IsRequired": false}, {"Name": "categoryId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "status", "Type": "System.String", "IsRequired": false}, {"Name": "isFeatured", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "language", "Type": "System.String", "IsRequired": false}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "sortBy", "Type": "System.String", "IsRequired": false}, {"Name": "sortOrder", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "ConstructionWebsite.API.Models.PagedResult`1[[ConstructionWebsite.API.DTOs.ProductDto, ConstructionWebsite.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ConstructionWebsite.API.Controllers.ProductsController", "Method": "CreateProduct", "RelativePath": "api/Products", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "productDto", "Type": "ConstructionWebsite.API.DTOs.CreateProductDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ConstructionWebsite.API.DTOs.ProductDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ConstructionWebsite.API.Controllers.ProductsController", "Method": "GetProduct", "RelativePath": "api/Products/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "language", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "ConstructionWebsite.API.DTOs.ProductDetailDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ConstructionWebsite.API.Controllers.ProductsController", "Method": "UpdateProduct", "RelativePath": "api/Products/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "productDto", "Type": "ConstructionWebsite.API.DTOs.UpdateProductDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ConstructionWebsite.API.DTOs.ProductDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ConstructionWebsite.API.Controllers.ProductsController", "Method": "DeleteProduct", "RelativePath": "api/Products/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ConstructionWebsite.API.Controllers.ProductsController", "Method": "CreateProductInquiry", "RelativePath": "api/Products/{id}/inquiries", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "inquiryDto", "Type": "ConstructionWebsite.API.DTOs.CreateProductInquiryDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ConstructionWebsite.API.DTOs.InquiryResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ConstructionWebsite.API.Controllers.ProductsController", "Method": "GetRelatedProducts", "RelativePath": "api/Products/{id}/related", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "language", "Type": "System.String", "IsRequired": false}, {"Name": "limit", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ConstructionWebsite.API.DTOs.ProductDto, ConstructionWebsite.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ConstructionWebsite.API.Controllers.ProductsController", "Method": "AddToWishlist", "RelativePath": "api/Products/{id}/wishlist", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ConstructionWebsite.API.Controllers.ProductsController", "Method": "RemoveFromWishlist", "RelativePath": "api/Products/{id}/wishlist", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ConstructionWebsite.API.Controllers.ProductsController", "Method": "GetWishlistStatus", "RelativePath": "api/Products/{id}/wishlist/status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "ConstructionWebsite.API.DTOs.WishlistStatusDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ConstructionWebsite.API.Controllers.ProductsController", "Method": "GetCategories", "RelativePath": "api/Products/categories", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "language", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ConstructionWebsite.API.DTOs.CategoryDto, ConstructionWebsite.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ConstructionWebsite.API.Controllers.ProductsController", "Method": "GetProductBySlug", "RelativePath": "api/Products/slug/{slug}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "slug", "Type": "System.String", "IsRequired": true}, {"Name": "language", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "ConstructionWebsite.API.DTOs.ProductDetailDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ConstructionWebsite.API.Controllers.ProjectsController", "Method": "GetProjects", "RelativePath": "api/Projects", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "search", "Type": "System.String", "IsRequired": false}, {"Name": "categoryId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "status", "Type": "System.String", "IsRequired": false}, {"Name": "isFeatured", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "language", "Type": "System.String", "IsRequired": false}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "sortBy", "Type": "System.String", "IsRequired": false}, {"Name": "sortOrder", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "ConstructionWebsite.API.Models.PagedResult`1[[ConstructionWebsite.API.DTOs.ProjectDto, ConstructionWebsite.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ConstructionWebsite.API.Controllers.ProjectsController", "Method": "CreateProject", "RelativePath": "api/Projects", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "projectDto", "Type": "ConstructionWebsite.API.DTOs.CreateProjectDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ConstructionWebsite.API.DTOs.ProjectDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ConstructionWebsite.API.Controllers.ProjectsController", "Method": "GetProject", "RelativePath": "api/Projects/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "language", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "ConstructionWebsite.API.DTOs.ProjectDetailDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ConstructionWebsite.API.Controllers.ProjectsController", "Method": "UpdateProject", "RelativePath": "api/Projects/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "projectDto", "Type": "ConstructionWebsite.API.DTOs.UpdateProjectDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ConstructionWebsite.API.DTOs.ProjectDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ConstructionWebsite.API.Controllers.ProjectsController", "Method": "DeleteProject", "RelativePath": "api/Projects/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ConstructionWebsite.API.Controllers.ProjectsController", "Method": "CreateProjectInquiry", "RelativePath": "api/Projects/{id}/inquiries", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "inquiryDto", "Type": "ConstructionWebsite.API.DTOs.CreateProjectInquiryDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ConstructionWebsite.API.DTOs.InquiryResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ConstructionWebsite.API.Controllers.ProjectsController", "Method": "GetRelatedProjects", "RelativePath": "api/Projects/{id}/related", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "language", "Type": "System.String", "IsRequired": false}, {"Name": "limit", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ConstructionWebsite.API.DTOs.ProjectDto, ConstructionWebsite.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ConstructionWebsite.API.Controllers.ProjectsController", "Method": "GetCategories", "RelativePath": "api/Projects/categories", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "language", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ConstructionWebsite.API.DTOs.CategoryDto, ConstructionWebsite.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ConstructionWebsite.API.Controllers.ProjectsController", "Method": "GetProjectBySlug", "RelativePath": "api/Projects/slug/{slug}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "slug", "Type": "System.String", "IsRequired": true}, {"Name": "language", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "ConstructionWebsite.API.DTOs.ProjectDetailDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ConstructionWebsite.API.Controllers.ServicesController", "Method": "GetServices", "RelativePath": "api/Services", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "search", "Type": "System.String", "IsRequired": false}, {"Name": "categoryId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "status", "Type": "System.String", "IsRequired": false}, {"Name": "isFeatured", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "language", "Type": "System.String", "IsRequired": false}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "sortBy", "Type": "System.String", "IsRequired": false}, {"Name": "sortOrder", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "ConstructionWebsite.API.Models.PagedResult`1[[ConstructionWebsite.API.DTOs.ServiceDto, ConstructionWebsite.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ConstructionWebsite.API.Controllers.ServicesController", "Method": "CreateService", "RelativePath": "api/Services", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "serviceDto", "Type": "ConstructionWebsite.API.DTOs.CreateServiceDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ConstructionWebsite.API.DTOs.ServiceDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ConstructionWebsite.API.Controllers.ServicesController", "Method": "GetService", "RelativePath": "api/Services/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "language", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "ConstructionWebsite.API.DTOs.ServiceDetailDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ConstructionWebsite.API.Controllers.ServicesController", "Method": "UpdateService", "RelativePath": "api/Services/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "serviceDto", "Type": "ConstructionWebsite.API.DTOs.UpdateServiceDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ConstructionWebsite.API.DTOs.ServiceDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ConstructionWebsite.API.Controllers.ServicesController", "Method": "DeleteService", "RelativePath": "api/Services/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "ConstructionWebsite.API.Controllers.ServicesController", "Method": "CreateServiceInquiry", "RelativePath": "api/Services/{id}/inquiries", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "inquiryDto", "Type": "ConstructionWebsite.API.DTOs.CreateServiceInquiryDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ConstructionWebsite.API.DTOs.InquiryResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ConstructionWebsite.API.Controllers.ServicesController", "Method": "GetRelatedServices", "RelativePath": "api/Services/{id}/related", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "language", "Type": "System.String", "IsRequired": false}, {"Name": "limit", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ConstructionWebsite.API.DTOs.ServiceDto, ConstructionWebsite.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ConstructionWebsite.API.Controllers.ServicesController", "Method": "GetCategories", "RelativePath": "api/Services/categories", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "language", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[ConstructionWebsite.API.DTOs.CategoryDto, ConstructionWebsite.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ConstructionWebsite.API.Controllers.ServicesController", "Method": "GetServiceBySlug", "RelativePath": "api/Services/slug/{slug}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "slug", "Type": "System.String", "IsRequired": true}, {"Name": "language", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "ConstructionWebsite.API.DTOs.ServiceDetailDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}]