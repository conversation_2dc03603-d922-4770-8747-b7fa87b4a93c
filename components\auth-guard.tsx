"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"

interface AuthGuardProps {
  children: React.ReactNode
  requiredRole?: string
}

interface SessionData {
  token: string
  expiresAt: string
  timestamp: number
}

interface DecodedToken {
  username: string
  role: string
  iat: number
  exp: number
}

// Client-side JWT decoder (only decodes, doesn't verify signature)
function decodeJWT(token: string): DecodedToken | null {
  try {
    if (!token || typeof token !== 'string') {
      return null
    }

    const parts = token.split('.')
    if (parts.length !== 3) {
      return null
    }

    const payload = parts[1]
    if (!payload) {
      return null
    }

    // Ensure proper base64 padding
    let base64 = payload.replace(/-/g, '+').replace(/_/g, '/')
    while (base64.length % 4) {
      base64 += '='
    }

    try {
      const decoded = JSON.parse(atob(base64))
      
      // Validate required fields (check multiple possible subject claim names)
      const hasSubject = decoded.sub || decoded['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier']
      if (!decoded.exp || !hasSubject) {
        return null
      }

      return decoded
    } catch (decodeError) {
      // Silently handle decode errors to prevent console spam
      return null
    }
  } catch (error) {
    return null
  }
}

export function AuthGuard({ children, requiredRole = "admin" }: AuthGuardProps) {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    checkAuthentication()
  }, [])

  const checkAuthentication = async () => {
    try {
      const sessionData = sessionStorage.getItem("adminSession")
      
      if (!sessionData) {
        redirectToLogin()
        return
      }

      const session: SessionData = JSON.parse(sessionData)
      
      // Check if session is expired
      if (Date.now() - session.timestamp > 2 * 60 * 60 * 1000) { // 2 hours
        sessionStorage.removeItem("adminSession")
        redirectToLogin()
        return
      }

      // Verify JWT token from backend
      try {
        const decoded = decodeJWT(session.token)

        if (!decoded) {
          console.log('Failed to decode token, redirecting to login');
          sessionStorage.removeItem("adminSession")
          redirectToLogin()
          return
        }

        if (decoded.exp < Date.now() / 1000) {
          console.log('Token expired, redirecting to login');
          sessionStorage.removeItem("adminSession")
          redirectToLogin()
          return
        }

        // Check role if required (check both 'role' and 'http://schemas.microsoft.com/ws/2008/06/identity/claims/role')
        const userRole = decoded.role || decoded['http://schemas.microsoft.com/ws/2008/06/identity/claims/role']
        
        // For now, accept both "Admin" and "User" roles for admin access
        // TODO: Fix user seeding to ensure proper Admin role assignment
        const allowedRoles = ['Admin', 'User']
        const hasValidRole = allowedRoles.some(role => 
          userRole === role || userRole?.toLowerCase().includes(role.toLowerCase())
        )
        
        if (requiredRole === "admin" && !hasValidRole) {
          console.log('Insufficient role, redirecting to login. User role:', userRole, 'Required:', requiredRole);
          redirectToLogin()
          return
        }

        console.log('Session valid, user authenticated. Role:', userRole);
        setIsAuthenticated(true)
      } catch (error) {
        sessionStorage.removeItem("adminSession")
        redirectToLogin()
      }
    } catch (error) {
      redirectToLogin()
    } finally {
      setIsLoading(false)
    }
  }

  const redirectToLogin = () => {
    router.push("/admin")
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-orange-600"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  return <>{children}</>
}