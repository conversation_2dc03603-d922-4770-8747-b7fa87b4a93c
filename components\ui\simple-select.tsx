"use client"

import * as React from "react"
import { ChevronDown } from "lucide-react"
import { cn } from "@/lib/utils"

interface SimpleSelectProps {
  value?: string
  onValueChange?: (value: string) => void
  placeholder?: string
  children: React.ReactNode
  className?: string
}

interface SimpleSelectItemProps {
  value: string
  children: React.ReactNode
  className?: string
}

const SimpleSelectContext = React.createContext<{
  value?: string
  onValueChange?: (value: string) => void
  isOpen: boolean
  setIsOpen: (open: boolean) => void
}>({
  isOpen: false,
  setIsOpen: () => {},
})

const SimpleSelect = ({ value, onValueChange, placeholder, children, className }: SimpleSelectProps) => {
  const [isOpen, setIsOpen] = React.useState(false)
  const selectRef = React.useRef<HTMLDivElement>(null)

  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  return (
    <SimpleSelectContext.Provider value={{ value, onValueChange, isOpen, setIsOpen }}>
      <div ref={selectRef} className={cn("relative", className)}>
        {children}
      </div>
    </SimpleSelectContext.Provider>
  )
}

const SimpleSelectTrigger = ({ children, className }: { children: React.ReactNode; className?: string }) => {
  const { isOpen, setIsOpen } = React.useContext(SimpleSelectContext)

  return (
    <button
      type="button"
      className={cn(
        "flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
        className
      )}
      onClick={() => setIsOpen(!isOpen)}
    >
      {children}
      <ChevronDown className="h-4 w-4 opacity-50" />
    </button>
  )
}

const SimpleSelectValue = ({ placeholder }: { placeholder?: string }) => {
  const { value } = React.useContext(SimpleSelectContext)
  const [displayValue, setDisplayValue] = React.useState<string>("")

  React.useEffect(() => {
    if (value) {
      // Find the display text for the selected value
      // This is a simplified approach - in a real implementation you'd want to
      // traverse the children to find the matching SelectItem
      setDisplayValue(value)
    } else {
      setDisplayValue("")
    }
  }, [value])

  return (
    <span className="line-clamp-1">
      {displayValue || placeholder}
    </span>
  )
}

const SimpleSelectContent = ({ children, className }: { children: React.ReactNode; className?: string }) => {
  const { isOpen } = React.useContext(SimpleSelectContext)

  if (!isOpen) return null

  return (
    <div
      className={cn(
        "absolute top-full left-0 z-50 w-full mt-1 max-h-96 overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95",
        className
      )}
    >
      <div className="p-1">
        {children}
      </div>
    </div>
  )
}

const SimpleSelectItem = ({ value, children, className }: SimpleSelectItemProps) => {
  const { onValueChange, setIsOpen, value: selectedValue } = React.useContext(SimpleSelectContext)

  const handleClick = () => {
    onValueChange?.(value)
    setIsOpen(false)
  }

  return (
    <div
      className={cn(
        "relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-2 text-sm outline-none hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
        selectedValue === value && "bg-accent text-accent-foreground",
        className
      )}
      onClick={handleClick}
    >
      {children}
    </div>
  )
}

export {
  SimpleSelect,
  SimpleSelectTrigger,
  SimpleSelectValue,
  SimpleSelectContent,
  SimpleSelectItem,
}
