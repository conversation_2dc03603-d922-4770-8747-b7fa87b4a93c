// API Configuration and Base Service
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'https://localhost:56266/api'

// Types for API responses
export interface ApiResponse<T> {
  success: boolean
  data?: T
  message?: string
  errors?: string[]
}

export interface PagedResult<T> {
  items: T[]
  totalCount: number
  pageNumber: number
  pageSize: number
  totalPages: number
  hasNextPage: boolean
  hasPreviousPage: boolean
}

// Product Types
export interface ProductDto {
  id: number
  name: string
  slug: string
  description: string
  shortDescription?: string
  price?: number
  imageUrl?: string
  category?: string
  isFeatured: boolean
  isActive: boolean
  viewCount: number
  createdAt: string
  updatedAt: string
}

// Project Types
export interface ProjectDto {
  id: number
  title: string
  slug: string
  description: string
  shortDescription?: string
  imageUrl?: string
  category?: string
  status: string
  isFeatured: boolean
  viewCount: number
  completedAt?: string
  createdAt: string
  updatedAt: string
}

// Service Types
export interface ServiceDto {
  id: number
  name: string
  slug: string
  description: string
  shortDescription?: string
  imageUrl?: string
  category?: string
  isFeatured: boolean
  isActive: boolean
  viewCount: number
  createdAt: string
  updatedAt: string
}

// Contact Message Types
export interface ContactDto {
  id: number
  name: string
  email: string
  phone?: string
  company?: string
  subject: string
  message: string
  inquiryType?: string
  preferredContactMethod?: string
  preferredContactTime?: string
  isRead: boolean
  isReplied: boolean
  reply?: string
  repliedAt?: string
  repliedBy?: string
  createdAt: string
  updatedAt: string
}

// Base API Service Class
class BaseApiService {
  protected baseUrl: string
  protected headers: HeadersInit

  constructor() {
    this.baseUrl = API_BASE_URL
    this.headers = {
      'Content-Type': 'application/json',
    }
  }

  protected getAuthHeaders(): HeadersInit {
    const headers = { ...this.headers }
    
    // Add authentication token if available (for admin operations)
    if (typeof window !== 'undefined') {
      const sessionRaw = sessionStorage.getItem('adminSession')
      if (sessionRaw) {
        try {
          const session = JSON.parse(sessionRaw)
          if (session.token) {
            headers['Authorization'] = `Bearer ${session.token}`
          }
        } catch (error) {
          console.warn('Failed to parse admin session:', error)
        }
      }
    }
    
    return headers
  }

  protected async request<T>(
    endpoint: string,
    options: RequestInit = {},
    useAuth = true
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`
    
    const config: RequestInit = {
      headers: useAuth ? this.getAuthHeaders() : this.headers,
      ...options,
      headers: {
        ...(useAuth ? this.getAuthHeaders() : this.headers),
        ...((options.headers as Record<string, string>) || {})
      }
    }

    try {
      const response = await fetch(url, config)
      
      if (!response.ok) {
        if (response.status === 401) {
          console.warn('Authentication failed. Please login again.')
          throw new Error('Authentication required')
        }
        
        if (response.status === 403) {
          console.warn('Access denied. Insufficient permissions.')
          throw new Error('Access denied')
        }
        
        let errorMessage = `HTTP error! status: ${response.status}`
        try {
          const errorData = await response.json()
          if (errorData.message) {
            errorMessage = errorData.message
          }
        } catch (e) {
          // Ignore JSON parsing errors for error responses
        }
        
        throw new Error(errorMessage)
      }

      const data = await response.json()
      return data
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error)
      throw error
    }
  }

  protected buildQueryString(params: Record<string, any>): string {
    const searchParams = new URLSearchParams()
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, String(value))
      }
    })
    
    return searchParams.toString()
  }
}

// Products API Service
export class ProductsApiService extends BaseApiService {
  async getProducts(params: {
    search?: string
    categoryId?: number
    status?: string
    isFeatured?: boolean
    language?: string
    page?: number
    pageSize?: number
    sortBy?: string
    sortOrder?: string
  } = {}): Promise<PagedResult<ProductDto>> {
    const queryString = this.buildQueryString(params)
    const endpoint = `/products${queryString ? `?${queryString}` : ''}`
    return this.request<PagedResult<ProductDto>>(endpoint, {}, false) // Public endpoint, no auth needed
  }

  async getProductsAdmin(params: {
    search?: string
    categoryId?: number
    status?: string
    isFeatured?: boolean
    language?: string
    page?: number
    pageSize?: number
    sortBy?: string
    sortOrder?: string
  } = {}): Promise<PagedResult<ProductDto>> {
    const queryString = this.buildQueryString(params)
    const endpoint = `/products/admin${queryString ? `?${queryString}` : ''}`
    return this.request<PagedResult<ProductDto>>(endpoint) // Admin endpoint, requires auth
  }

  async getProductById(id: number, language = 'en'): Promise<ProductDto> {
    return this.request<ProductDto>(`/products/${id}?language=${language}`)
  }

  async getProductBySlug(slug: string, language = 'en'): Promise<ProductDto> {
    return this.request<ProductDto>(`/products/slug/${slug}?language=${language}`)
  }
}

// Projects API Service
export class ProjectsApiService extends BaseApiService {
  async getProjects(params: {
    search?: string
    categoryId?: number
    status?: string
    isFeatured?: boolean
    language?: string
    page?: number
    pageSize?: number
    sortBy?: string
    sortOrder?: string
  } = {}): Promise<PagedResult<ProjectDto>> {
    const queryString = this.buildQueryString(params)
    const endpoint = `/projects${queryString ? `?${queryString}` : ''}`
    return this.request<PagedResult<ProjectDto>>(endpoint)
  }

  async getProjectById(id: number, language = 'en'): Promise<ProjectDto> {
    return this.request<ProjectDto>(`/projects/${id}?language=${language}`)
  }

  async getProjectBySlug(slug: string, language = 'en'): Promise<ProjectDto> {
    return this.request<ProjectDto>(`/projects/slug/${slug}?language=${language}`)
  }
}

// Services API Service
export class ServicesApiService extends BaseApiService {
  async getServices(params: {
    search?: string
    categoryId?: number
    status?: string
    isFeatured?: boolean
    language?: string
    page?: number
    pageSize?: number
    sortBy?: string
    sortOrder?: string
  } = {}): Promise<PagedResult<ServiceDto>> {
    const queryString = this.buildQueryString(params)
    const endpoint = `/services${queryString ? `?${queryString}` : ''}`
    return this.request<PagedResult<ServiceDto>>(endpoint)
  }

  async getServiceById(id: number, language = 'en'): Promise<ServiceDto> {
    return this.request<ServiceDto>(`/services/${id}?language=${language}`)
  }

  async getServiceBySlug(slug: string, language = 'en'): Promise<ServiceDto> {
    return this.request<ServiceDto>(`/services/slug/${slug}?language=${language}`)
  }
}

// Contact Messages API Service (Note: This would need a ContactController in the backend)
export class ContactApiService extends BaseApiService {
  // For now, we'll create placeholder methods that would work with a ContactController
  async getContactMessages(): Promise<ContactDto[]> {
    // This would need a ContactController endpoint like /api/contact
    // For now, return empty array as fallback
    try {
      return this.request<ContactDto[]>('/contact')
    } catch (error) {
      console.warn('Contact API not available, using fallback')
      return []
    }
  }

  async getContactMessageById(id: number): Promise<ContactDto | null> {
    try {
      return this.request<ContactDto>(`/contact/${id}`)
    } catch (error) {
      console.warn('Contact API not available, using fallback')
      return null
    }
  }

  async markAsRead(id: number): Promise<boolean> {
    try {
      await this.request(`/contact/${id}/mark-read`, { method: 'PATCH' })
      return true
    } catch (error) {
      console.warn('Contact API not available, using fallback')
      return false
    }
  }
}

// Export service instances
export const productsApi = new ProductsApiService()
export const projectsApi = new ProjectsApiService()
export const servicesApi = new ServicesApiService()
export const contactApi = new ContactApiService()

// Dashboard API Service - combines all services for dashboard use
export class DashboardApiService {
  async getDashboardStats() {
    try {
      // Try to get basic stats from available endpoints
      let totalProducts = 0
      let totalProjects = 0
      let totalServices = 0
      let pendingMessages = 0

      // Try to get products count
      try {
        const productsResult = await productsApi.getProducts({ pageSize: 1 })
        totalProducts = productsResult.totalCount || 0
      } catch (error) {
        console.warn('Could not fetch products from API:', error)
      }

      // Try to get projects count
      try {
        const projectsResult = await projectsApi.getProjects({ pageSize: 1 })
        totalProjects = projectsResult.totalCount || 0
      } catch (error) {
        console.warn('Could not fetch projects from API:', error)
      }

      // Try to get services count
      try {
        const servicesResult = await servicesApi.getServices({ pageSize: 1 })
        totalServices = servicesResult.totalCount || 0
      } catch (error) {
        console.warn('Could not fetch services from API:', error)
      }

      // Try to get contact messages (may fail if ContactController doesn't exist)
      try {
        const messages = await contactApi.getContactMessages()
        pendingMessages = messages.filter(m => !m.isRead).length
      } catch (error) {
        console.warn('Could not fetch contact messages from API:', error)
      }

      return {
        totalProducts,
        totalProjects,
        totalServices,
        pendingMessages
      }
    } catch (error) {
      console.error('Failed to fetch dashboard stats from API:', error)
      throw error
    }
  }

  async getRecentProducts(limit = 5) {
    try {
      const result = await productsApi.getProducts({ 
        pageSize: limit, 
        sortBy: 'createdAt', 
        sortOrder: 'desc' 
      })
      return result.items || []
    } catch (error) {
      console.warn('Could not fetch recent products from API:', error)
      return []
    }
  }

  async getRecentProjects(limit = 5) {
    try {
      const result = await projectsApi.getProjects({ 
        pageSize: limit, 
        sortBy: 'createdAt', 
        sortOrder: 'desc' 
      })
      return result.items || []
    } catch (error) {
      console.warn('Could not fetch recent projects from API:', error)
      return []
    }
  }

  async getRecentServices(limit = 5) {
    try {
      const result = await servicesApi.getServices({ 
        pageSize: limit, 
        sortBy: 'createdAt', 
        sortOrder: 'desc' 
      })
      return result.items || []
    } catch (error) {
      console.warn('Could not fetch recent services from API:', error)
      return []
    }
  }

  async getRecentMessages(limit = 5) {
    try {
      const messages = await contactApi.getContactMessages()
      return messages
        .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
        .slice(0, limit)
    } catch (error) {
      console.warn('Could not fetch recent messages from API:', error)
      return []
    }
  }
}

export const dashboardApi = new DashboardApiService()