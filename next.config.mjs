/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: false,
  images: {
    domains: ['localhost', 'drylexiraq.com', 'www.drylexiraq.com', 'drylex.com', 'www.drylex.com'],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'drylexiraq.com',
        port: '',
        pathname: '/images/**',
      },
      {
        protocol: 'https',
        hostname: 'www.drylexiraq.com',
        port: '',
        pathname: '/images/**',
      },
      {
        protocol: 'https',
        hostname: 'drylex.com',
        port: '',
        pathname: '/images/**',
      },
      {
        protocol: 'https',
        hostname: 'www.drylex.com',
        port: '',
        pathname: '/images/**',
      }
    ],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    loader: 'default',
    path: '/',
    unoptimized: false
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  // Temporarily disabled headers for debugging
  // headers: async () => {
  //   return [];
  // },
}

export default nextConfig
